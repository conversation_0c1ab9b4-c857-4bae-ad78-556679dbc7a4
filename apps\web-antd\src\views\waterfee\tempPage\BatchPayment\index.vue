<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import {
  CheckOutlined,
  DeleteOutlined,
  PlusOutlined,
  PrinterOutlined,
  ReloadOutlined,
} from '@ant-design/icons-vue';
import {
  Button,
  Card,
  Checkbox,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
  Space,
  Statistic,
  Table,
  Tag,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { columns, mockUserData, querySchema, userTableColumns } from './data';

// 引入表单组件
const FormItem = Form.Item;

// 表格用户数据
const userListData = ref(mockUserData);
const selectedUserKeys = ref([]);

// 批量选择用户相关状态
const selectedUsers = ref([]);
const userSelectionVisible = ref(false);
const userSearchValue = ref('');

// 支付相关状态
const paymentAmount = ref(0);
const isPaymentModalVisible = ref(false);
const paymentMethod = ref('1'); // 默认现金支付
const paymentLoading = ref(false);
const paymentRemark = ref('');

// 发票打印相关状态
const isPrintModalVisible = ref(false);
const printLoading = ref(false);
const printAll = ref(true);
const selectedPrintUsers = ref([]);

// 计算总金额
const totalAmount = ref(0);

// 表单配置
const formOptions = {
  commonConfig: {
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
};

// 表格配置
const gridOptions = {
  columns,
  height: 'auto',
  data: userListData,
  id: 'waterfee-batch-payment-list-index',
};

// 只保留tableApi，忽略第一个返回值
const { 1: tableApi } = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 用户选择对话框中的表格列
const userTableProps = {
  columns: userTableColumns,
  dataSource: mockUserData,
  rowKey: 'userId',
  pagination: {
    pageSize: 5,
  },
  rowSelection: {
    selectedRowKeys: selectedUserKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      selectedUserKeys.value = selectedRowKeys;
      selectedUsers.value = selectedRows;
    },
  },
};

// 过滤用户列表
const filteredUserData = computed(() => {
  if (!userSearchValue.value) return mockUserData;
  return mockUserData.filter(
    (user) =>
      user.userNo.includes(userSearchValue.value) ||
      user.userName.includes(userSearchValue.value) ||
      user.address.includes(userSearchValue.value),
  );
});

// 刷新表格
function handleRefresh() {
  tableApi.reload();
  message.success('数据已刷新');
}

// 打开用户选择对话框
function openUserSelection() {
  userSelectionVisible.value = true;
  selectedUserKeys.value = [];
  selectedUsers.value = [];
}

// 确认选择用户
function confirmUserSelection() {
  if (selectedUsers.value.length === 0) {
    message.warning('请至少选择一个用户');
    return;
  }

  // 更新用户列表和总金额
  userListData.value = selectedUsers.value.map((user) => ({
    ...user,
    paymentStatus: '0', // 未支付
  }));

  calculateTotalAmount();
  userSelectionVisible.value = false;
  message.success(`已选择${selectedUsers.value.length}个用户`);
}

// 计算总金额
function calculateTotalAmount() {
  totalAmount.value = userListData.value.reduce(
    (sum, user) => sum + (user.outstandingAmount || 0),
    0,
  );
  paymentAmount.value = totalAmount.value;
}

// 删除选中用户
function removeSelectedUser(userId) {
  userListData.value = userListData.value.filter(
    (user) => user.userId !== userId,
  );
  calculateTotalAmount();
  message.success('已移除用户');
}

// 打开支付对话框
function openPaymentModal() {
  if (userListData.value.length === 0) {
    message.warning('请先选择用户');
    return;
  }

  isPaymentModalVisible.value = true;
}

// 确认支付
function confirmPayment() {
  if (paymentAmount.value <= 0) {
    message.warning('支付金额必须大于0');
    return;
  }

  paymentLoading.value = true;

  // 模拟支付过程
  setTimeout(() => {
    // 更新支付状态
    userListData.value = userListData.value.map((user) => ({
      ...user,
      paymentStatus: '1', // 已支付
      paymentMethod: paymentMethod.value,
      paymentTime: new Date().toISOString(),
      transactionId: `TX${Date.now()}${Math.floor(Math.random() * 1000)}`,
    }));

    paymentLoading.value = false;
    isPaymentModalVisible.value = false;
    message.success('批量支付成功');
  }, 1000);
}

// 打开打印对话框
function openPrintModal() {
  if (
    userListData.value.filter((user) => user.paymentStatus === '1').length === 0
  ) {
    message.warning('没有已支付的记录可打印');
    return;
  }

  isPrintModalVisible.value = true;
  printAll.value = true;
  selectedPrintUsers.value = userListData.value
    .filter((user) => user.paymentStatus === '1')
    .map((user) => user.userId);
}

// 确认打印
function confirmPrint() {
  printLoading.value = true;

  // 模拟打印过程
  setTimeout(() => {
    printLoading.value = false;
    isPrintModalVisible.value = false;

    const printCount = printAll.value
      ? userListData.value.filter((user) => user.paymentStatus === '1').length
      : selectedPrintUsers.value.length;

    message.success(`已成功打印${printCount}份收据`);
  }, 1500);
}

// 计算已选中的打印用户
const printUserRows = computed(() => {
  return userListData.value.filter(
    (user) =>
      user.paymentStatus === '1' &&
      (printAll.value || selectedPrintUsers.value.includes(user.userId)),
  );
});

// 组件挂载时计算总金额
onMounted(() => {
  calculateTotalAmount();
});
</script>

<template>
  <Page :auto-content-height="true">
    <div class="batch-payment-container">
      <!-- 标题与操作按钮 -->
      <div class="header-actions">
        <h2>批量收费管理</h2>
        <Space>
          <Button type="primary" @click="openUserSelection">
            <template #icon><PlusOutlined /></template>
            选择用户
          </Button>
          <Button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </Button>
          <Button
            type="primary"
            @click="openPaymentModal"
            :disabled="userListData.length === 0"
          >
            <template #icon><CheckOutlined /></template>
            批量缴费
          </Button>
          <Button
            type="primary"
            ghost
            @click="openPrintModal"
            :disabled="
              userListData.filter((u) => u.paymentStatus === '1').length === 0
            "
          >
            <template #icon><PrinterOutlined /></template>
            打印收据
          </Button>
        </Space>
      </div>

      <!-- 统计卡片 -->
      <div class="statistics-container">
        <Card class="statistic-card">
          <Statistic
            title="选中用户数"
            :value="userListData.length"
            :value-style="{ color: '#1890ff' }"
          />
        </Card>
        <Card class="statistic-card">
          <Statistic
            title="待缴总金额(元)"
            :value="totalAmount"
            precision="2"
            :value-style="{ color: '#cf1322' }"
          />
        </Card>
        <Card class="statistic-card">
          <Statistic
            title="已缴费用户"
            :value="userListData.filter((u) => u.paymentStatus === '1').length"
            :value-style="{ color: '#3f8600' }"
          />
        </Card>
      </div>

      <!-- 用户表格 -->
      <Card title="用户缴费列表" class="user-table-card">
        <Table
          :data-source="userListData"
          :columns="columns"
          row-key="userId"
          :pagination="{ pageSize: 10 }"
        >
          <!-- 操作列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'operation'">
              <Button
                type="link"
                danger
                @click="removeSelectedUser(record.userId)"
                :disabled="record.paymentStatus === '1'"
              >
                <DeleteOutlined />
              </Button>
            </template>
            <!-- 支付状态列 -->
            <template v-if="column.dataIndex === 'paymentStatus'">
              <Tag :color="record.paymentStatus === '1' ? 'green' : 'orange'">
                {{ record.paymentStatus === '1' ? '已支付' : '未支付' }}
              </Tag>
            </template>
          </template>
        </Table>
      </Card>
    </div>

    <!-- 用户选择对话框 -->
    <Modal
      title="选择用户"
      v-model:visible="userSelectionVisible"
      width="900px"
      @ok="confirmUserSelection"
    >
      <div class="user-search">
        <Input
          v-model:value="userSearchValue"
          placeholder="搜索用户编号、名称或地址"
          style="width: 300px; margin-bottom: 16px"
          allow-clear
        />
      </div>
      <Table v-bind="userTableProps" :data-source="filteredUserData" />
    </Modal>

    <!-- 支付对话框 -->
    <Modal
      title="批量缴费确认"
      v-model:visible="isPaymentModalVisible"
      :confirm-loading="paymentLoading"
      @ok="confirmPayment"
      ok-text="确认支付"
    >
      <Form layout="vertical">
        <FormItem label="支付总金额">
          <InputNumber
            v-model:value="paymentAmount"
            style="width: 100%"
            :min="0.01"
            :precision="2"
            :formatter="
              (value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            "
            :parser="(value) => value.replace(/\¥\s?|(,*)/g, '')"
          />
        </FormItem>
        <FormItem label="支付方式">
          <Select v-model:value="paymentMethod" style="width: 100%">
            <Select.Option value="1">现金支付</Select.Option>
            <Select.Option value="2">银行转账</Select.Option>
            <Select.Option value="3">微信支付</Select.Option>
            <Select.Option value="4">支付宝</Select.Option>
            <Select.Option value="5">POS机</Select.Option>
          </Select>
        </FormItem>
        <FormItem label="备注">
          <Input v-model:value="paymentRemark" placeholder="请输入备注信息" />
        </FormItem>
      </Form>
      <div class="payment-summary">
        <p>将为以下用户进行批量缴费：</p>
        <ul>
          <li
            v-for="(user, index) in userListData"
            :key="user.userId"
            v-show="index < 3"
          >
            {{ user.userNo }} - {{ user.userName }} -
            {{ user.outstandingAmount }}元
          </li>
          <li v-if="userListData.length > 3">
            ...等{{ userListData.length }}个用户
          </li>
        </ul>
      </div>
    </Modal>

    <!-- 打印收据对话框 -->
    <Modal
      title="打印收据"
      v-model:visible="isPrintModalVisible"
      :confirm-loading="printLoading"
      @ok="confirmPrint"
      ok-text="开始打印"
    >
      <div class="print-options">
        <Checkbox v-model:checked="printAll">为所有已缴费用户打印收据</Checkbox>

        <div v-if="!printAll" class="print-user-selection">
          <Divider>选择需要打印收据的用户</Divider>
          <Checkbox.Group v-model:value="selectedPrintUsers">
            <div
              v-for="user in userListData.filter(
                (u) => u.paymentStatus === '1',
              )"
              :key="user.userId"
              class="print-user-item"
            >
              <Checkbox :value="user.userId">
                {{ user.userNo }} - {{ user.userName }} -
                {{ user.outstandingAmount }}元
              </Checkbox>
            </div>
          </Checkbox.Group>
        </div>
      </div>

      <div class="print-summary">
        <Divider>打印预览</Divider>
        <p>将打印 {{ printUserRows.length }} 份收据</p>
      </div>
    </Modal>
  </Page>
</template>

<style scoped>
.batch-payment-container {
  padding: 16px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.statistics-container {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.statistic-card {
  flex: 1;
}

.user-table-card {
  margin-bottom: 16px;
}

.payment-summary,
.print-summary {
  margin-top: 16px;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
}

.print-user-selection {
  max-height: 200px;
  overflow-y: auto;
}

.print-user-item {
  margin-bottom: 8px;
}
</style>
