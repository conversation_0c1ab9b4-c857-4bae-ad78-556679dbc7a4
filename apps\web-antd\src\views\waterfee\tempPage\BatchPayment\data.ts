import type { FormSchemaGetter } from '#/adapter/form';

/**
 * @description 表格列
 */
export const columns = [
  {
    title: '用户编号',
    dataIndex: 'userNo',
    width: 120,
    align: 'center',
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 120,
    align: 'center',
  },
  {
    title: '用户地址',
    dataIndex: 'address',
    width: 250,
    align: 'center',
  },
  {
    title: '用户类型',
    dataIndex: 'userType',
    width: 100,
    align: 'center',
    customRender: ({ text }: { text: string }) => {
      const typeMap: Record<string, string> = {
        '1': '居民用户',
        '2': '企业用户',
        '3': '政府机构',
      };
      return typeMap[text] || text;
    },
  },
  {
    title: '未缴金额(元)',
    dataIndex: 'outstandingAmount',
    width: 120,
    align: 'center',
  },
  {
    title: '账单周期',
    dataIndex: 'billPeriod',
    width: 120,
    align: 'center',
  },
  {
    title: '支付状态',
    dataIndex: 'paymentStatus',
    width: 100,
    align: 'center',
  },
  {
    title: '支付方式',
    dataIndex: 'paymentMethod',
    width: 120,
    align: 'center',
    customRender: ({ text }: { text: string }) => {
      const methodMap: Record<string, string> = {
        '1': '现金支付',
        '2': '银行转账',
        '3': '微信支付',
        '4': '支付宝',
        '5': 'POS机',
      };
      return text ? methodMap[text] || text : '-';
    },
  },
  {
    title: '支付时间',
    dataIndex: 'paymentTime',
    width: 170,
    align: 'center',
    customRender: ({ text }: { text: string }) => {
      return text ? new Date(text).toLocaleString() : '-';
    },
  },
  {
    title: '交易流水号',
    dataIndex: 'transactionId',
    width: 180,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 80,
    fixed: 'right',
    align: 'center',
  },
];

/**
 * @description 用户选择表格列
 */
export const userTableColumns = [
  {
    title: '用户编号',
    dataIndex: 'userNo',
    width: 120,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 120,
  },
  {
    title: '用户地址',
    dataIndex: 'address',
    width: 250,
  },
  {
    title: '用户类型',
    dataIndex: 'userType',
    width: 100,
    customRender: ({ text }: { text: string }) => {
      const typeMap: Record<string, string> = {
        '1': '居民用户',
        '2': '企业用户',
        '3': '政府机构',
      };
      return typeMap[text] || text;
    },
  },
  {
    title: '未缴金额(元)',
    dataIndex: 'outstandingAmount',
    width: 120,
  },
  {
    title: '账单周期',
    dataIndex: 'billPeriod',
    width: 120,
  },
];

/**
 * @description 查询表单
 */
export const querySchema: FormSchemaGetter = () => [
  {
    fieldName: 'userNo',
    label: '用户编号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户编号',
    },
  },
  {
    fieldName: 'userName',
    label: '用户名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户名',
    },
  },
  {
    fieldName: 'userType',
    label: '用户类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择用户类型',
      options: [
        { label: '居民用户', value: '1' },
        { label: '企业用户', value: '2' },
        { label: '政府机构', value: '3' },
      ],
    },
  },
  {
    fieldName: 'billPeriod',
    label: '账单周期',
    component: 'Select',
    componentProps: {
      placeholder: '请选择账单周期',
      options: [
        { label: '2023年12月', value: '2023-12' },
        { label: '2024年1月', value: '2024-01' },
        { label: '2024年2月', value: '2024-02' },
        { label: '2024年3月', value: '2024-03' },
        { label: '2024年4月', value: '2024-04' },
      ],
    },
  },
  {
    fieldName: 'paymentStatus',
    label: '支付状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择支付状态',
      options: [
        { label: '未支付', value: '0' },
        { label: '已支付', value: '1' },
      ],
    },
  },
];

/**
 * @description 模拟用户数据
 */
export const mockUserData = [
  {
    userId: '1001',
    userNo: 'USER20240101',
    userName: '张三',
    address: '江苏省南京市鼓楼区中山北路123号',
    userType: '1',
    outstandingAmount: 235.5,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1002',
    userNo: 'USER20240102',
    userName: '李四',
    address: '江苏省南京市玄武区北京东路45号',
    userType: '1',
    outstandingAmount: 186.2,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1003',
    userNo: 'USER20240103',
    userName: '王五',
    address: '江苏省南京市秦淮区中华路78号',
    userType: '1',
    outstandingAmount: 198.75,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1004',
    userNo: 'COMP20240104',
    userName: '南京科技有限公司',
    address: '江苏省南京市江宁区天元东路88号',
    userType: '2',
    outstandingAmount: 1568.4,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1005',
    userNo: 'USER20240105',
    userName: '赵六',
    address: '江苏省南京市建邺区嘉陵江东街12号',
    userType: '1',
    outstandingAmount: 215.3,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1006',
    userNo: 'GOV20240106',
    userName: '南京市第一中学',
    address: '江苏省南京市鼓楼区中山路156号',
    userType: '3',
    outstandingAmount: 3256.8,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1007',
    userNo: 'USER20240107',
    userName: '孙七',
    address: '江苏省南京市栖霞区文苑路9号',
    userType: '1',
    outstandingAmount: 178.9,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1008',
    userNo: 'COMP20240108',
    userName: '南京电子科技有限公司',
    address: '江苏省南京市浦口区浦滨路128号',
    userType: '2',
    outstandingAmount: 2145.6,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1009',
    userNo: 'USER20240109',
    userName: '周八',
    address: '江苏省南京市雨花台区雨花南路66号',
    userType: '1',
    outstandingAmount: 201.35,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1010',
    userNo: 'GOV20240110',
    userName: '南京市人民医院',
    address: '江苏省南京市玄武区中央路321号',
    userType: '3',
    outstandingAmount: 4578.2,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1011',
    userNo: 'USER20240111',
    userName: '吴九',
    address: '江苏省南京市栖霞区仙林大道15号',
    userType: '1',
    outstandingAmount: 189.6,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
  {
    userId: '1012',
    userNo: 'USER20240112',
    userName: '郑十',
    address: '江苏省南京市江宁区东山街道25号',
    userType: '1',
    outstandingAmount: 195.7,
    billPeriod: '2024-04',
    paymentStatus: '0',
    paymentMethod: '',
    paymentTime: '',
    transactionId: '',
  },
];
