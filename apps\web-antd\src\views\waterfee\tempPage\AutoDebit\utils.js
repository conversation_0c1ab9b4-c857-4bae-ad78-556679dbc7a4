/**
 * 日期格式化函数
 * @param {Date|string|number} date - 要格式化的日期
 * @param {string} format - 格式化模式，例如：YYYY-MM-DD HH:mm:ss
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  const d = date instanceof Date ? date : new Date(date);

  if (Number.isNaN(d.getTime())) {
    console.error('Invalid date:', date);
    return String(date);
  }

  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hours = d.getHours();
  const minutes = d.getMinutes();
  const seconds = d.getSeconds();

  // 补零函数
  const padZero = (num) => (num < 10 ? `0${num}` : `${num}`);

  // 替换格式化字符
  return format
    .replace(/YYYY/g, `${year}`)
    .replace(/MM/g, padZero(month))
    .replace(/DD/g, padZero(day))
    .replace(/HH/g, padZero(hours))
    .replace(/mm/g, padZero(minutes))
    .replace(/ss/g, padZero(seconds));
}
