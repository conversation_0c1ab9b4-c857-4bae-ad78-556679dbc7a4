/**
 * @description 文件格式选项
 */
export const fileFormatOptions = [
  { label: '文本文件(TXT)', value: 'txt' },
  { label: '逗号分隔值(CSV)', value: 'csv' },
  { label: 'Excel文件(XLSX)', value: 'xlsx' },
  { label: 'Excel旧版(XLS)', value: 'xls' },
  { label: 'XML文件', value: 'xml' },
];

/**
 * @description 文件编码选项
 */
export const encodingOptions = [
  { label: 'utf8', value: 'utf8' },
  { label: 'gbk', value: 'gbk' },
  { label: 'gb2312', value: 'gb2312' },
  { label: 'gb18030', value: 'gb18030' },
  { label: 'utf16', value: 'utf16' },
  { label: 'ascii', value: 'ascii' },
];

/**
 * @description 字段类型选项
 */
export const fieldTypeOptions = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '日期', value: 'date' },
  { label: '金额', value: 'amount' },
  { label: '布尔值', value: 'boolean' },
];

/**
 * @description 模拟银行代扣规则数据
 */
export const mockBankRules = [
  {
    id: 'rule_1',
    bankId: 'ICBC',
    bankName: '中国工商银行',
    fileFormat: 'txt',
    fieldSeparator: ',',
    encoding: 'gbk',
    hasHeader: true,
    isActive: true,
    defaultPath: '/export/debit/icbc/',
    fileNameTemplate: 'ICBC_{YYYYMMDD}.txt',
    description: '工商银行代扣文件格式，要求GBK编码，逗号分隔，含表头',
    fields: [
      {
        position: 1,
        fieldName: 'userNo',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '用户编号',
      },
      {
        position: 2,
        fieldName: 'userName',
        fieldType: 'string',
        fieldLength: 30,
        defaultValue: '',
        isRequired: true,
        description: '用户姓名',
      },
      {
        position: 3,
        fieldName: 'bankAccount',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '银行账号',
      },
      {
        position: 4,
        fieldName: 'amount',
        fieldType: 'amount',
        fieldLength: 12,
        defaultValue: '0.00',
        isRequired: true,
        description: '代扣金额',
      },
      {
        position: 5,
        fieldName: 'billPeriod',
        fieldType: 'string',
        fieldLength: 10,
        defaultValue: '',
        isRequired: true,
        description: '账单周期',
      },
      {
        position: 6,
        fieldName: 'remark',
        fieldType: 'string',
        fieldLength: 50,
        defaultValue: '',
        isRequired: false,
        description: '备注',
      },
    ],
  },
  {
    id: 'rule_2',
    bankId: 'CCB',
    bankName: '中国建设银行',
    fileFormat: 'csv',
    fieldSeparator: ',',
    encoding: 'utf8',
    hasHeader: true,
    isActive: true,
    defaultPath: '/export/debit/ccb/',
    fileNameTemplate: 'CCB_DEBIT_{YYYYMMDD}.csv',
    description: '建设银行代扣文件格式，要求UTF-8编码，CSV格式，含表头',
    fields: [
      {
        position: 1,
        fieldName: 'userNo',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '用户编号',
      },
      {
        position: 2,
        fieldName: 'userName',
        fieldType: 'string',
        fieldLength: 30,
        defaultValue: '',
        isRequired: true,
        description: '用户姓名',
      },
      {
        position: 3,
        fieldName: 'idCard',
        fieldType: 'string',
        fieldLength: 18,
        defaultValue: '',
        isRequired: true,
        description: '身份证号',
      },
      {
        position: 4,
        fieldName: 'bankAccount',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '银行账号',
      },
      {
        position: 5,
        fieldName: 'amount',
        fieldType: 'amount',
        fieldLength: 12,
        defaultValue: '0.00',
        isRequired: true,
        description: '代扣金额',
      },
      {
        position: 6,
        fieldName: 'debitDate',
        fieldType: 'date',
        fieldLength: 10,
        defaultValue: '',
        isRequired: true,
        description: '代扣日期',
      },
      {
        position: 7,
        fieldName: 'billPeriod',
        fieldType: 'string',
        fieldLength: 10,
        defaultValue: '',
        isRequired: true,
        description: '账单周期',
      },
    ],
  },
  {
    id: 'rule_3',
    bankId: 'ABC',
    bankName: '中国农业银行',
    fileFormat: 'txt',
    fieldSeparator: '|',
    encoding: 'gb2312',
    hasHeader: false,
    isActive: true,
    defaultPath: '/export/debit/abc/',
    fileNameTemplate: 'ABC_{YYYYMMDD}.txt',
    description: '农业银行代扣文件格式，要求GB2312编码，竖线分隔，无表头',
    fields: [
      {
        position: 1,
        fieldName: 'batchNo',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '批次号',
      },
      {
        position: 2,
        fieldName: 'userNo',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '用户编号',
      },
      {
        position: 3,
        fieldName: 'userName',
        fieldType: 'string',
        fieldLength: 30,
        defaultValue: '',
        isRequired: true,
        description: '用户姓名',
      },
      {
        position: 4,
        fieldName: 'bankAccount',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '银行账号',
      },
      {
        position: 5,
        fieldName: 'amount',
        fieldType: 'amount',
        fieldLength: 12,
        defaultValue: '0.00',
        isRequired: true,
        description: '代扣金额',
      },
      {
        position: 6,
        fieldName: 'billPeriod',
        fieldType: 'string',
        fieldLength: 10,
        defaultValue: '',
        isRequired: true,
        description: '账单周期',
      },
    ],
  },
  {
    id: 'rule_4',
    bankId: 'BOC',
    bankName: '中国银行',
    fileFormat: 'xlsx',
    fieldSeparator: ',',
    encoding: 'utf8',
    hasHeader: true,
    isActive: true,
    defaultPath: '/export/debit/boc/',
    fileNameTemplate: 'BOC_DEBIT_{YYYYMMDD}.xlsx',
    description: '中国银行代扣文件格式，要求Excel格式，含表头',
    fields: [
      {
        position: 1,
        fieldName: 'userNo',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '用户编号',
      },
      {
        position: 2,
        fieldName: 'userName',
        fieldType: 'string',
        fieldLength: 30,
        defaultValue: '',
        isRequired: true,
        description: '用户姓名',
      },
      {
        position: 3,
        fieldName: 'bankAccount',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '银行账号',
      },
      {
        position: 4,
        fieldName: 'accountType',
        fieldType: 'string',
        fieldLength: 2,
        defaultValue: '01',
        isRequired: true,
        description: '账户类型(01:储蓄账户,02:信用卡)',
      },
      {
        position: 5,
        fieldName: 'amount',
        fieldType: 'amount',
        fieldLength: 12,
        defaultValue: '0.00',
        isRequired: true,
        description: '代扣金额',
      },
      {
        position: 6,
        fieldName: 'billPeriod',
        fieldType: 'string',
        fieldLength: 10,
        defaultValue: '',
        isRequired: true,
        description: '账单周期',
      },
      {
        position: 7,
        fieldName: 'remark',
        fieldType: 'string',
        fieldLength: 50,
        defaultValue: '',
        isRequired: false,
        description: '备注',
      },
    ],
  },
  {
    id: 'rule_5',
    bankId: 'COMM',
    bankName: '交通银行',
    fileFormat: 'txt',
    fieldSeparator: '\t',
    encoding: 'gbk',
    hasHeader: true,
    isActive: false,
    defaultPath: '/export/debit/comm/',
    fileNameTemplate: 'COMM_{YYYYMMDD}.txt',
    description: '交通银行代扣文件格式，要求GBK编码，制表符分隔，含表头',
    fields: [
      {
        position: 1,
        fieldName: 'orgCode',
        fieldType: 'string',
        fieldLength: 10,
        defaultValue: 'ORG001',
        isRequired: true,
        description: '机构代码',
      },
      {
        position: 2,
        fieldName: 'userNo',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '用户编号',
      },
      {
        position: 3,
        fieldName: 'userName',
        fieldType: 'string',
        fieldLength: 30,
        defaultValue: '',
        isRequired: true,
        description: '用户姓名',
      },
      {
        position: 4,
        fieldName: 'bankAccount',
        fieldType: 'string',
        fieldLength: 20,
        defaultValue: '',
        isRequired: true,
        description: '银行账号',
      },
      {
        position: 5,
        fieldName: 'amount',
        fieldType: 'amount',
        fieldLength: 12,
        defaultValue: '0.00',
        isRequired: true,
        description: '代扣金额',
      },
      {
        position: 6,
        fieldName: 'billPeriod',
        fieldType: 'string',
        fieldLength: 10,
        defaultValue: '',
        isRequired: true,
        description: '账单周期',
      },
    ],
  },
];
