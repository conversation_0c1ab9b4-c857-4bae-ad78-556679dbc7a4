<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  DeleteOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';
import {
  Button,
  Card,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Table,
  Tag,
  Upload,
} from 'ant-design-vue';

import { mockBankRules } from './data';
import {
  batchTypeOptions,
  formatBatchType,
  formatStatus,
  getStatusColor,
  mockBatchData,
  statusOptions,
} from './managementData';
import { formatDate } from './utils';

// 表单组件
const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 状态
const isGenerateVisible = ref(false);
const isImportVisible = ref(false);
const loadingTable = ref(false);
const loadingImport = ref(false);
const loadingGenerate = ref(false);

// 查询参数
const queryParams = reactive({
  bankId: '',
  dateRange: [],
  batchType: 'all',
  status: 'all',
});

// 生成代扣表单
const generateFormRef = ref();
const generateFormState = reactive({
  bankId: '',
  accountPeriod: '',
  debitDate: null,
  remark: '',
});

// 导入返盘表单
const importFormRef = ref();
const importFormState = reactive({
  bankId: '',
  fileList: [],
});

// 银行列表
const bankOptions = mockBankRules
  .filter((rule) => rule.isActive)
  .map((rule) => ({
    label: rule.bankName,
    value: rule.bankId,
  }));

// 文件导入配置
const uploadProps = {
  beforeUpload: (file) => {
    importFormState.fileList = [...importFormState.fileList, file];
    return false;
  },
  remove: (file) => {
    const index = importFormState.fileList.indexOf(file);
    const newFileList = [...importFormState.fileList];
    newFileList.splice(index, 1);
    importFormState.fileList = newFileList;
  },
  fileList: importFormState.fileList,
};

// 表格数据
const tableData = ref(mockBatchData);

// 表格列配置
const columns = [
  {
    title: '批次号',
    dataIndex: 'batchNo',
    key: 'batchNo',
    width: 140,
  },
  {
    title: '银行名称',
    dataIndex: 'bankName',
    key: 'bankName',
    width: 130,
  },
  {
    title: '批次类型',
    dataIndex: 'batchType',
    key: 'batchType',
    width: 100,
  },
  {
    title: '账期',
    dataIndex: 'accountPeriod',
    key: 'accountPeriod',
    width: 100,
  },
  {
    title: '总笔数',
    dataIndex: 'totalCount',
    key: 'totalCount',
    width: 90,
  },
  {
    title: '成功笔数',
    dataIndex: 'successCount',
    key: 'successCount',
    width: 90,
  },
  {
    title: '失败笔数',
    dataIndex: 'failCount',
    key: 'failCount',
    width: 90,
  },
  {
    title: '总金额',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: 110,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    key: 'operation',
    width: 280,
    fixed: 'right',
  },
];

// 表单验证规则
const generateRules = {
  bankId: [{ required: true, message: '请选择银行', trigger: 'change' }],
  accountPeriod: [{ required: true, message: '请输入账期', trigger: 'blur' }],
  debitDate: [{ required: true, message: '请选择代扣日期', trigger: 'change' }],
};

const importRules = {
  bankId: [{ required: true, message: '请选择银行', trigger: 'change' }],
  fileList: [
    { required: true, validator: validateFileList, trigger: 'change' },
  ],
};

// 自定义验证文件列表
function validateFileList(rule, value, callback) {
  if (importFormState.fileList.length === 0) {
    callback(new Error('请选择要导入的文件'));
  } else {
    callback();
  }
}

// 查询列表
function handleQuery() {
  loadingTable.value = true;
  // 实际应用中这里应该调用API获取数据
  setTimeout(() => {
    // 模拟筛选逻辑
    const filteredData = tableData.value.filter((item) => {
      // 银行筛选
      if (queryParams.bankId && item.bankId !== queryParams.bankId) {
        return false;
      }

      // 状态筛选
      if (queryParams.status !== 'all' && item.status !== queryParams.status) {
        return false;
      }

      // 批次类型筛选
      if (
        queryParams.batchType !== 'all' &&
        item.batchType !== queryParams.batchType
      ) {
        return false;
      }

      return true;
    });

    // 更新表格数据
    tableData.value = filteredData;
    loadingTable.value = false;
    message.success('查询成功');
  }, 500);
}

// 重置查询
function resetQuery() {
  queryParams.bankId = '';
  queryParams.dateRange = [];
  queryParams.batchType = 'all';
  queryParams.status = 'all';
  handleQuery();
}

// 打开生成代扣文件弹窗
function openGenerateModal() {
  isGenerateVisible.value = true;
  // 重置表单
  generateFormState.bankId = '';
  generateFormState.accountPeriod = formatDate(new Date(), 'YYYY年MM月');
  generateFormState.debitDate = null;
  generateFormState.remark = '';
}

// 生成代扣文件
function handleGenerate() {
  generateFormRef.value.validate().then(() => {
    loadingGenerate.value = true;

    // 模拟生成文件
    setTimeout(() => {
      // 生成一个新批次
      const newBatch = {
        id: `batch_${Date.now()}`,
        batchNo: `DK${formatDate(new Date(), 'YYYYMMDD')}${Math.floor(
          Math.random() * 1000,
        )
          .toString()
          .padStart(3, '0')}`,
        bankId: generateFormState.bankId,
        bankName:
          bankOptions.find((item) => item.value === generateFormState.bankId)
            ?.label || '',
        batchType: 'regular',
        accountPeriod: generateFormState.accountPeriod,
        createTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss'),
        sendTime: null,
        importTime: null,
        totalCount: Math.floor(Math.random() * 3000) + 500,
        successCount: 0,
        failCount: 0,
        totalAmount: Number.parseFloat(
          (Math.random() * 500_000 + 10_000).toFixed(2),
        ),
        status: 'pending',
        fileName: `${generateFormState.bankId}_${formatDate(new Date(), 'YYYYMMDD')}.txt`,
        returnFileName: null,
        remark: generateFormState.remark,
      };

      tableData.value.unshift(newBatch);
      loadingGenerate.value = false;
      isGenerateVisible.value = false;
      message.success('代扣文件生成成功');
    }, 1000);
  });
}

// 打开导入返盘文件弹窗
function openImportModal() {
  isImportVisible.value = true;
  // 重置表单
  importFormState.bankId = '';
  importFormState.fileList = [];
}

// 导入返盘文件
function handleImport() {
  importFormRef.value.validate().then(() => {
    loadingImport.value = true;

    // 模拟导入文件
    setTimeout(() => {
      // 找到对应银行的最新已发送批次
      const batchIndex = tableData.value.findIndex(
        (item) =>
          item.bankId === importFormState.bankId && item.status === 'sent',
      );

      if (batchIndex === -1) {
        message.warning('未找到该银行待处理的代扣批次');
      } else {
        // 更新批次状态
        tableData.value[batchIndex].status = 'completed';
        tableData.value[batchIndex].importTime = formatDate(
          new Date(),
          'YYYY-MM-DD HH:mm:ss',
        );
        tableData.value[batchIndex].returnFileName =
          `${importFormState.bankId}_RETURN_${formatDate(new Date(), 'YYYYMMDD')}.txt`;

        // 随机生成成功和失败笔数
        const totalCount = tableData.value[batchIndex].totalCount;
        const failCount = Math.floor(Math.random() * (totalCount * 0.05));
        tableData.value[batchIndex].successCount = totalCount - failCount;
        tableData.value[batchIndex].failCount = failCount;

        message.success('返盘文件导入成功，已完成销账处理');
      }

      loadingImport.value = false;
      isImportVisible.value = false;
    }, 1000);
  });
}

// 发送代扣文件
function handleSend(record) {
  const index = tableData.value.findIndex((item) => item.id === record.id);
  if (index !== -1) {
    tableData.value[index].status = 'sent';
    tableData.value[index].sendTime = formatDate(
      new Date(),
      'YYYY-MM-DD HH:mm:ss',
    );
    message.success('代扣文件发送成功');
  }
}

// 删除批次
function handleDelete(id) {
  tableData.value = tableData.value.filter((item) => item.id !== id);
  message.success('批次删除成功');
}

// 路由
const router = useRouter();

// 查看详情
function handleViewDetail() {
  router.push({
    path: `/businessCharges/chargeManagement/manage/detail`,
  });
}

// 查看明细
function handleViewItems() {
  router.push({
    path: `/businessCharges/chargeManagement/manage/detail`,
  });
}

// 导出文件
function handleExport(record) {
  message.success(`导出文件：${record.fileName}`);
  // 实际应用中应该调用API下载文件
}

// 导出返盘文件
function handleExportReturn(record) {
  message.success(`导出返盘文件：${record.returnFileName}`);
  // 实际应用中应该调用API下载文件
}

// 初始化
onMounted(() => {
  // 可以在这里从API加载数据
  handleQuery();
});
</script>

<template>
  <Page :auto-content-height="true">
    <div class="auto-debit-management-container">
      <!-- 头部标题和操作按钮 -->
      <Card class="header-card">
        <div class="header-title">
          <h2>代扣管理</h2>
          <p>生成代扣文件，导出发送代扣文件给银行，导入银行返盘文件销账</p>
        </div>
        <div class="header-actions">
          <Space>
            <Button type="primary" @click="openGenerateModal">
              <template #icon><PlusOutlined /></template>
              生成代扣文件
            </Button>
            <Button type="primary" @click="openImportModal">
              <template #icon><ImportOutlined /></template>
              导入返盘文件
            </Button>
          </Space>
        </div>
      </Card>

      <!-- 查询区域 -->
      <Card class="query-card">
        <Form layout="inline" :model="queryParams">
          <FormItem label="银行">
            <Select
              v-model:value="queryParams.bankId"
              placeholder="选择银行"
              style="width: 150px"
              allow-clear
            >
              <Option
                v-for="bank in bankOptions"
                :key="bank.value"
                :value="bank.value"
              >
                {{ bank.label }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="创建日期">
            <RangePicker
              v-model:value="queryParams.dateRange"
              style="width: 240px"
            />
          </FormItem>
          <FormItem label="批次类型">
            <Select v-model:value="queryParams.batchType" style="width: 120px">
              <Option
                v-for="option in batchTypeOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="状态">
            <Select v-model:value="queryParams.status" style="width: 120px">
              <Option
                v-for="option in statusOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </Option>
            </Select>
          </FormItem>
          <FormItem>
            <Space>
              <Button type="primary" @click="handleQuery">
                <template #icon><SearchOutlined /></template>
                查询
              </Button>
              <Button @click="resetQuery"> 重置 </Button>
            </Space>
          </FormItem>
        </Form>
      </Card>

      <!-- 批次列表 -->
      <Card title="代扣批次列表" class="batch-list-card">
        <Table
          :data-source="tableData"
          :columns="columns"
          row-key="id"
          :pagination="{
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条记录`,
          }"
          :loading="loadingTable"
          bordered
          :scroll="{ x: 1500 }"
        >
          <!-- 自定义表格内容 -->
          <template #bodyCell="{ column, record }">
            <!-- 批次类型 -->
            <template v-if="column.key === 'batchType'">
              {{ formatBatchType(record.batchType) }}
            </template>

            <!-- 总金额 -->
            <template v-if="column.key === 'totalAmount'">
              {{
                record.totalAmount.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
              }}
            </template>

            <!-- 状态 -->
            <template v-if="column.key === 'status'">
              <Tag :color="getStatusColor(record.status)">
                {{ formatStatus(record.status) }}
              </Tag>
            </template>

            <!-- 操作按钮 -->
            <template v-if="column.key === 'operation'">
              <Space>
                <Button type="primary" size="small" @click="handleViewDetail()">
                  <template #icon><FileTextOutlined /></template>
                  详情
                </Button>
                <Button type="primary" size="small" @click="handleViewItems()">
                  <template #icon><FileExcelOutlined /></template>
                  明细
                </Button>
                <Button
                  type="primary"
                  size="small"
                  @click="handleExport(record)"
                >
                  <template #icon><DownloadOutlined /></template>
                  导出
                </Button>
                <Button
                  v-if="record.status === 'pending'"
                  type="primary"
                  size="small"
                  @click="handleSend(record)"
                >
                  <template #icon><UploadOutlined /></template>
                  发送
                </Button>
                <Button
                  v-if="record.status === 'completed'"
                  type="primary"
                  size="small"
                  @click="handleExportReturn(record)"
                >
                  <template #icon><DownloadOutlined /></template>
                  导出返盘
                </Button>
                <Popconfirm
                  v-if="record.status === 'pending'"
                  title="确定要删除该批次吗?"
                  @confirm="handleDelete(record.id)"
                  ok-text="确定"
                  cancel-text="取消"
                >
                  <Button type="primary" danger size="small">
                    <template #icon><DeleteOutlined /></template>
                    删除
                  </Button>
                </Popconfirm>
              </Space>
            </template>
          </template>
        </Table>
      </Card>
    </div>

    <!-- 生成代扣文件模态框 -->
    <Modal
      title="生成代扣文件"
      v-model:visible="isGenerateVisible"
      width="500px"
      :confirm-loading="loadingGenerate"
      @ok="handleGenerate"
      :mask-closable="false"
    >
      <Form
        ref="generateFormRef"
        :model="generateFormState"
        :rules="generateRules"
        layout="vertical"
      >
        <FormItem label="选择银行" name="bankId">
          <Select
            v-model:value="generateFormState.bankId"
            placeholder="请选择银行"
            style="width: 100%"
          >
            <Option
              v-for="bank in bankOptions"
              :key="bank.value"
              :value="bank.value"
            >
              {{ bank.label }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="账期" name="accountPeriod">
          <Input
            v-model:value="generateFormState.accountPeriod"
            placeholder="请输入账期，如：2023年07月"
          />
        </FormItem>
        <FormItem label="代扣日期" name="debitDate">
          <DatePicker
            v-model:value="generateFormState.debitDate"
            style="width: 100%"
            placeholder="请选择代扣日期"
          />
        </FormItem>
        <FormItem label="批次备注" name="remark">
          <Input.TextArea
            v-model:value="generateFormState.remark"
            placeholder="请输入批次备注"
            :rows="3"
          />
        </FormItem>
      </Form>
    </Modal>

    <!-- 导入返盘文件模态框 -->
    <Modal
      title="导入返盘文件"
      v-model:visible="isImportVisible"
      width="500px"
      :confirm-loading="loadingImport"
      @ok="handleImport"
      :mask-closable="false"
    >
      <Form
        ref="importFormRef"
        :model="importFormState"
        :rules="importRules"
        layout="vertical"
      >
        <FormItem label="选择银行" name="bankId">
          <Select
            v-model:value="importFormState.bankId"
            placeholder="请选择银行"
            style="width: 100%"
          >
            <Option
              v-for="bank in bankOptions"
              :key="bank.value"
              :value="bank.value"
            >
              {{ bank.label }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="上传返盘文件" name="fileList">
          <Upload
            v-bind="uploadProps"
            :file-list="importFormState.fileList"
            accept=".txt,.csv,.xlsx,.xls"
          >
            <Button>
              <template #icon><UploadOutlined /></template>
              选择文件
            </Button>
            <div class="upload-hint">支持 txt, csv, xlsx, xls 格式文件</div>
          </Upload>
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style scoped>
.auto-debit-management-container {
  padding: 16px;
}

.header-card {
  margin-bottom: 16px;
}

.header-title {
  margin-bottom: 16px;
}

.header-title h2 {
  margin-bottom: 8px;
}

.header-title p {
  color: rgba(0, 0, 0, 0.45);
  margin: 0;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
}

.query-card {
  margin-bottom: 16px;
}

.batch-list-card {
  margin-bottom: 16px;
}

.upload-hint {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 8px;
}
</style>
