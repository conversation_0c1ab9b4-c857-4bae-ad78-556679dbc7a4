<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  Button as AButton,
  Card as ACard,
  message,
  Tabs,
} from 'ant-design-vue';

import { areaInfo } from '#/api/waterfee/area';
import { getIntelligentMeterInfo } from '#/api/waterfee/meter/intelligent';
import { meterBookInfo } from '#/api/waterfee/meterbook';
import { getLatestReading } from '#/api/waterfee/meterReadingRecord';
import { getUser } from '#/api/waterfee/user/archivesManage';
import { Description, useDescription } from '#/components/description';
import { preserveBigInt } from '#/utils/json-bigint';

import MeterChangeRecords from '../../components/MeterChangeRecords.vue';
// 导入组件
import ReadingRecords from '../components/ReadingRecords.vue';
import { getDescSchema } from './intelligent.data';

const { TabPane } = Tabs;

// 获取路由参数
const route = useRoute();
const router = useRouter();
const meterId = ref('');
const activeKey = ref('reading'); // 默认显示抄表记录标签页
const loading = ref(false);

// 状态定义
const detailInfo = ref({});
const title = computed(() => '智能水表详情');

// 使用Description组件
const [registerDescription, { setDescProps }] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: [],
});

// 获取区域和表册信息
async function fetchAreaAndMeterBookInfo(meterData) {
  try {
    // 获取区域信息
    if (meterData.businessAreaId) {
      const areaData = await areaInfo(meterData.businessAreaId);
      if (areaData) {
        meterData.businessAreaName = areaData.areaName;
      }
    }

    // 获取表册信息
    if (meterData.meterBookId) {
      const bookData = await meterBookInfo(meterData.meterBookId);
      if (bookData) {
        meterData.meterBookName = bookData.bookName;
      }
    }
  } catch (error) {
    console.error('获取区域和表册信息失败:', error);
  }
}

// 获取用户名称
async function fetchUserName(meterData) {
  try {
    if (meterData.userId) {
      const userData = await getUser(meterData.userId);
      if (userData) {
        meterData.userName = userData.userName || '无用户名称';
      }
    }
  } catch (error) {
    console.error('获取用户名称失败:', error);
    meterData.userName = '获取失败';
  }
}

// 获取最新抄表数据
async function fetchLatestReading(meterData) {
  try {
    if (meterData.meterNo) {
      const readingData = await getLatestReading(meterData.meterNo);
      if (readingData) {
        meterData.currentReading = readingData.lastReading || 0;
      }
    }
  } catch (error) {
    console.error('获取最新抄表数据失败:', error);
    meterData.currentReading = '获取失败';
  }
}

// 返回列表页
function handleBack() {
  router.back();
}

// 初始化数据
const initData = async () => {
  const { id } = route.query;
  if (!id) {
    message.error('缺少水表ID参数');
    return;
  }

  meterId.value = String(id);
  console.log('详情页获取到水表ID:', meterId.value);

  loading.value = true;
  try {
    // 直接获取数据，因为 requestClient 已经处理了响应结果
    const data = await getIntelligentMeterInfo(meterId.value);
    // console.log('获取水表详情成功:', JSON.stringify(data, null, 2));
    detailInfo.value = preserveBigInt(data);

    // 获取区域和表册信息
    await fetchAreaAndMeterBookInfo(detailInfo.value);

    // 获取用户名称和最新抄表数据
    await Promise.all([
      fetchUserName(detailInfo.value),
      fetchLatestReading(detailInfo.value),
    ]);

    // console.log('获取区域和表册信息后的数据:', JSON.stringify(detailInfo.value, null, 2));

    // 设置描述项数据
    // console.log('设置描述项数据:', JSON.stringify(detailInfo.value, null, 2));
    setDescProps(
      {
        data: detailInfo.value,
        schema: getDescSchema(detailInfo.value),
        title: '水表基本信息',
      },
      true,
    );
  } catch (error) {
    console.error('获取水表详情失败:', error);
    message.error(error.message || '获取水表详情失败');
  } finally {
    loading.value = false;
  }
};

// // 监听数据变化
// watch(detailInfo, (newVal) => {
//   console.log('数据变化:', JSON.stringify(newVal, null, 2));
// });

// 组件挂载时初始化
onMounted(() => {
  console.log('组件挂载');
  initData();
});
</script>

<template>
  <div class="p-4">
    <ACard :title="title" :loading="loading">
      <template #extra>
        <AButton @click="handleBack">返回</AButton>
      </template>
      <Description @register="registerDescription" />

      <div class="mt-4">
        <Tabs v-model:active-key="activeKey">
          <TabPane key="reading" tab="抄表记录">
            <ReadingRecords
              :meter-id="meterId"
              :meter-no="detailInfo.meterNo"
            />
          </TabPane>
          <TabPane key="change" tab="换表记录">
            <MeterChangeRecords
              :meter-id="meterId"
              :meter-no="detailInfo.meterNo"
            />
          </TabPane>
        </Tabs>
      </div>
    </ACard>
  </div>
</template>

<style scoped>
.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.form-item {
  display: flex;
  margin-bottom: 16px;
}

.form-item label {
  width: 120px;
  padding-right: 12px;
  font-weight: bold;
  color: rgb(0 0 0 / 65%);
  text-align: right;
}

.form-item span {
  color: rgb(0 0 0 / 85%);
}

.form-title {
  padding-left: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  border-left: 3px solid #1890ff;
}
</style>
