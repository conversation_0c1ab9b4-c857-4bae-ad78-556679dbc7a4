import { defineComponent } from 'vue';

/**
 * 代扣配置页面
 */
export const AutoDebitConfig = defineComponent({
  name: 'AutoDebitConfig',
  setup() {
    return () => import('./index.vue');
  },
});

/**
 * 代扣管理页面
 */
export const AutoDebitManagement = defineComponent({
  name: 'AutoDebitManagement',
  setup() {
    return () => import('./Management.vue');
  },
});

/**
 * 代扣详情页面
 */
export const AutoDebitDetail = defineComponent({
  name: 'AutoDebitDetail',
  setup() {
    return () => import('./DebitDetail.vue');
  },
});

export default AutoDebitConfig;
