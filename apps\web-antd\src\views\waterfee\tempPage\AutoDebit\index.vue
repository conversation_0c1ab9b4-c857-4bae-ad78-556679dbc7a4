<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import {
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
  SettingOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';
import {
  Button,
  Card,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Switch,
  Table,
  Tabs,
  Tag,
  Tooltip,
} from 'ant-design-vue';

import {
  encodingOptions,
  fieldTypeOptions,
  fileFormatOptions,
  mockBankRules,
} from './data';

// 表单组件
const FormItem = Form.Item;
const { TabPane } = Tabs;
const { Option } = Select;

// 状态
const bankRules = ref(mockBankRules);
const selectedRule = ref(null);
const isModalVisible = ref(false);
const isFieldsModalVisible = ref(false);
const activeTabKey = ref('1');
const modalTitle = ref('新增代扣规则');
const isEditing = ref(false);

// 表单
const formRef = ref();
const fieldFormRef = ref();
const formState = reactive({
  bankId: '',
  bankName: '',
  fileFormat: 'txt',
  fieldSeparator: ',',
  encoding: 'utf8',
  hasHeader: true,
  isActive: true,
  description: '',
  defaultPath: '/export/debit/',
  fileNameTemplate: 'DK_{YYYYMMDD}_{BANK}.txt',
});

// 字段配置
const currentFields = ref([]);
const fieldFormState = reactive({
  fieldName: '',
  fieldType: 'string',
  fieldLength: 20,
  defaultValue: '',
  isRequired: true,
  position: 0,
  description: '',
});

// 格式化字段分隔符显示
function formatSeparator(separator) {
  if (separator === ',') {
    return '逗号(,)';
  }
  if (separator === '|') {
    return '竖线(|)';
  }
  if (separator === '\t') {
    return '制表符(Tab)';
  }
  return `${separator}`;
}

// 表格列配置
const columns = [
  {
    title: '银行代码',
    dataIndex: 'bankId',
    key: 'bankId',
    width: 120,
  },
  {
    title: '银行名称',
    dataIndex: 'bankName',
    key: 'bankName',
    width: 180,
  },
  {
    title: '文件格式',
    dataIndex: 'fileFormat',
    key: 'fileFormat',
    width: 100,
    customRender: ({ text }) => {
      return (
        fileFormatOptions.find((item) => item.value === text)?.label || text
      );
    },
  },
  {
    title: '字段分隔符',
    dataIndex: 'fieldSeparator',
    key: 'fieldSeparator',
    width: 100,
    customRender: ({ text }) => formatSeparator(text),
  },
  {
    title: '文件编码',
    dataIndex: 'encoding',
    key: 'encoding',
    width: 120,
  },
  {
    title: '是否含表头',
    dataIndex: 'hasHeader',
    key: 'hasHeader',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'isActive',
    key: 'isActive',
    width: 100,
  },
  {
    title: '字段数',
    dataIndex: 'fields',
    key: 'fields',
    width: 100,
    customRender: ({ text }) => {
      return text ? text.length : 0;
    },
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'operation',
    width: 250,
    fixed: 'right',
  },
];

// 字段配置表格列
const fieldColumns = [
  {
    title: '序号',
    dataIndex: 'position',
    key: 'position',
    width: 80,
  },
  {
    title: '字段名称',
    dataIndex: 'fieldName',
    key: 'fieldName',
    width: 150,
  },
  {
    title: '字段类型',
    dataIndex: 'fieldType',
    key: 'fieldType',
    width: 120,
    customRender: ({ text }) => {
      return (
        fieldTypeOptions.find((item) => item.value === text)?.label || text
      );
    },
  },
  {
    title: '字段长度',
    dataIndex: 'fieldLength',
    key: 'fieldLength',
    width: 100,
  },
  {
    title: '默认值',
    dataIndex: 'defaultValue',
    key: 'defaultValue',
    width: 120,
  },
  {
    title: '是否必填',
    dataIndex: 'isRequired',
    key: 'isRequired',
    width: 100,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'operation',
    width: 150,
    fixed: 'right',
  },
];

// 表单验证规则
const rules = {
  bankId: [{ required: true, message: '请输入银行代码', trigger: 'blur' }],
  bankName: [{ required: true, message: '请输入银行名称', trigger: 'blur' }],
  fileFormat: [
    { required: true, message: '请选择文件格式', trigger: 'change' },
  ],
  encoding: [{ required: true, message: '请选择文件编码', trigger: 'change' }],
};

// 字段表单验证规则
const fieldRules = {
  fieldName: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
  fieldType: [{ required: true, message: '请选择字段类型', trigger: 'change' }],
  fieldLength: [{ required: true, message: '请输入字段长度', trigger: 'blur' }],
  position: [{ required: true, message: '请输入字段位置', trigger: 'blur' }],
};

// 打开新增规则模态框
function handleAdd() {
  isEditing.value = false;
  modalTitle.value = '新增代扣规则';
  resetForm();
  isModalVisible.value = true;
}

// 打开编辑规则模态框
function handleEdit(record) {
  isEditing.value = true;
  modalTitle.value = '编辑代扣规则';

  // 复制记录值到表单
  Object.keys(formState).forEach((key) => {
    if (key in record) {
      formState[key] = record[key];
    }
  });

  selectedRule.value = record;
  isModalVisible.value = true;
}

// 删除规则
function handleDelete(id) {
  bankRules.value = bankRules.value.filter((rule) => rule.id !== id);
  message.success('删除成功');
}

// 配置字段
function handleConfigFields(record) {
  selectedRule.value = record;
  currentFields.value = [...(record.fields || [])];
  isFieldsModalVisible.value = true;
}

// 保存表单
function handleSave() {
  formRef.value.validate().then(() => {
    if (isEditing.value) {
      // 更新现有规则
      const index = bankRules.value.findIndex(
        (rule) => rule.id === selectedRule.value.id,
      );
      if (index !== -1) {
        bankRules.value[index] = {
          ...bankRules.value[index],
          ...formState,
        };
        message.success('更新成功');
      }
    } else {
      // 添加新规则
      const newRule = {
        id: `rule_${Date.now()}`,
        ...formState,
        fields: [],
      };
      bankRules.value.push(newRule);
      message.success('添加成功');
    }

    isModalVisible.value = false;
  });
}

// 添加字段
function handleAddField() {
  // 获取下一个可用的位置
  const nextPosition =
    currentFields.value.length > 0
      ? Math.max(...currentFields.value.map((f) => f.position)) + 1
      : 1;

  // 重置字段表单
  resetFieldForm(nextPosition);

  // 打开表单
  activeTabKey.value = '2';
}

// 重置字段表单
function resetFieldForm(position) {
  fieldFormState.position = position || 1;
  fieldFormState.fieldName = '';
  fieldFormState.fieldType = 'string';
  fieldFormState.fieldLength = 20;
  fieldFormState.defaultValue = '';
  fieldFormState.isRequired = true;
  fieldFormState.description = '';
}

// 编辑字段
function handleEditField(field) {
  // 复制字段值到表单
  Object.keys(fieldFormState).forEach((key) => {
    if (key in field) {
      fieldFormState[key] = field[key];
    }
  });

  // 切换到表单标签页
  activeTabKey.value = '2';
}

// 删除字段
function handleDeleteField(position) {
  currentFields.value = currentFields.value.filter(
    (field) => field.position !== position,
  );
  message.success('字段已删除');
}

// 保存字段
function handleSaveField() {
  fieldFormRef.value.validate().then(() => {
    // 检查是否已存在相同位置的字段
    const existingIndex = currentFields.value.findIndex(
      (f) => f.position === fieldFormState.position,
    );

    if (existingIndex === -1) {
      // 添加新字段
      currentFields.value.push({ ...fieldFormState });
    } else {
      // 更新现有字段
      currentFields.value[existingIndex] = { ...fieldFormState };
    }

    // 按位置排序
    currentFields.value.sort((a, b) => a.position - b.position);

    // 切换回列表标签页
    activeTabKey.value = '1';
    message.success('字段已保存');
  });
}

// 保存字段配置
function handleSaveFields() {
  const index = bankRules.value.findIndex(
    (rule) => rule.id === selectedRule.value.id,
  );
  if (index !== -1) {
    bankRules.value[index].fields = [...currentFields.value];
    message.success('字段配置已保存');
    isFieldsModalVisible.value = false;
  }
}

// 重置表单
function resetForm() {
  formState.bankId = '';
  formState.bankName = '';
  formState.fileFormat = 'txt';
  formState.fieldSeparator = ',';
  formState.encoding = 'utf8';
  formState.hasHeader = true;
  formState.isActive = true;
  formState.description = '';
  formState.defaultPath = '/export/debit/';
  formState.fileNameTemplate = 'DK_{YYYYMMDD}_{BANK}.txt';
}

// 生成代扣文件
function handleGenerateFile(rule) {
  message.success(`已生成${rule.bankName}的代扣文件`);
}

// 导出配置
function handleExportConfig() {
  message.success('代扣配置已导出');
}

// 导入配置
function handleImportConfig() {
  message.success('代扣配置已导入');
}

// 初始化
onMounted(() => {
  // 可以在这里从API加载数据
});
</script>

<template>
  <Page :auto-content-height="true">
    <div class="auto-debit-container">
      <!-- 头部标题和操作按钮 -->
      <Card class="header-card">
        <div class="header-title">
          <h2>代扣配置管理</h2>
          <p>配置代扣银行的代扣规则，包括生成的代扣文件的字段、格式、编码等</p>
        </div>
        <div class="header-actions">
          <Space>
            <Button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              新增规则
            </Button>
            <Button @click="handleExportConfig">
              <template #icon><DownloadOutlined /></template>
              导出配置
            </Button>
            <Button @click="handleImportConfig">
              <template #icon><UploadOutlined /></template>
              导入配置
            </Button>
          </Space>
        </div>
      </Card>

      <!-- 规则列表 -->
      <Card title="代扣规则列表" class="rule-list-card">
        <Table
          :data-source="bankRules"
          :columns="columns"
          row-key="id"
          :pagination="{ pageSize: 10 }"
          bordered
        >
          <!-- 自定义表格内容 -->
          <template #bodyCell="{ column, record }">
            <!-- 是否含表头 -->
            <template v-if="column.key === 'hasHeader'">
              <Tag :color="record.hasHeader ? 'green' : 'orange'">
                {{ record.hasHeader ? '是' : '否' }}
              </Tag>
            </template>

            <!-- 状态 -->
            <template v-if="column.key === 'isActive'">
              <Tag :color="record.isActive ? 'green' : 'red'">
                {{ record.isActive ? '启用' : '禁用' }}
              </Tag>
            </template>

            <!-- 操作按钮 -->
            <template v-if="column.key === 'operation'">
              <Space>
                <Button type="primary" size="small" @click="handleEdit(record)">
                  <template #icon><EditOutlined /></template>
                  编辑
                </Button>
                <Button
                  type="primary"
                  size="small"
                  @click="handleConfigFields(record)"
                >
                  <template #icon><SettingOutlined /></template>
                  字段配置
                </Button>
                <Tooltip title="生成代扣文件">
                  <Button
                    type="primary"
                    size="small"
                    ghost
                    @click="handleGenerateFile(record)"
                  >
                    <template #icon><DownloadOutlined /></template>
                  </Button>
                </Tooltip>
                <Popconfirm
                  title="确定要删除该规则吗?"
                  @confirm="handleDelete(record.id)"
                  ok-text="确定"
                  cancel-text="取消"
                >
                  <Button type="primary" danger size="small">
                    <template #icon><DeleteOutlined /></template>
                    删除
                  </Button>
                </Popconfirm>
              </Space>
            </template>
          </template>
        </Table>
      </Card>
    </div>

    <!-- 新增/编辑规则模态框 -->
    <Modal
      :title="modalTitle"
      v-model:visible="isModalVisible"
      width="700px"
      @ok="handleSave"
      :mask-closable="false"
    >
      <Form
        ref="formRef"
        :model="formState"
        :rules="rules"
        layout="vertical"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <FormItem label="银行代码" name="bankId">
          <Input
            v-model:value="formState.bankId"
            placeholder="请输入银行代码，如: ICBC"
          />
        </FormItem>
        <FormItem label="银行名称" name="bankName">
          <Input
            v-model:value="formState.bankName"
            placeholder="请输入银行名称，如: 工商银行"
          />
        </FormItem>

        <Divider>文件格式配置</Divider>

        <FormItem label="文件格式" name="fileFormat">
          <Select
            v-model:value="formState.fileFormat"
            placeholder="请选择文件格式"
          >
            <Option
              v-for="option in fileFormatOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="字段分隔符" name="fieldSeparator">
          <Select
            v-model:value="formState.fieldSeparator"
            placeholder="请选择字段分隔符"
          >
            <Option value=",">逗号(,)</Option>
            <Option value="|">竖线(|)</Option>
            <Option value="\t">制表符(Tab)</Option>
          </Select>
        </FormItem>
        <FormItem label="文件编码" name="encoding">
          <Select
            v-model:value="formState.encoding"
            placeholder="请选择文件编码"
          >
            <Option
              v-for="option in encodingOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="是否含表头" name="hasHeader">
          <Switch v-model:checked="formState.hasHeader" />
        </FormItem>
        <FormItem label="默认导出路径" name="defaultPath">
          <Input
            v-model:value="formState.defaultPath"
            placeholder="请输入默认导出路径"
          />
        </FormItem>
        <FormItem label="文件名模板" name="fileNameTemplate">
          <Input
            v-model:value="formState.fileNameTemplate"
            placeholder="请输入文件名模板，支持变量：{YYYYMMDD}、{BANK}"
          />
          <div class="help-text">
            支持变量：{YYYYMMDD}=日期，{BANK}=银行代码
          </div>
        </FormItem>

        <Divider>其他设置</Divider>

        <FormItem label="状态" name="isActive">
          <Switch v-model:checked="formState.isActive" />
        </FormItem>
        <FormItem label="描述" name="description">
          <Input.TextArea
            v-model:value="formState.description"
            placeholder="请输入描述信息"
            :rows="3"
          />
        </FormItem>
      </Form>
    </Modal>

    <!-- 字段配置模态框 -->
    <Modal
      title="字段配置"
      v-model:visible="isFieldsModalVisible"
      width="900px"
      @ok="handleSaveFields"
      :mask-closable="false"
    >
      <div v-if="selectedRule" class="field-config-header">
        <h3>
          {{ selectedRule.bankName }} ({{ selectedRule.bankId }}) 的字段配置
        </h3>
        <p>配置代扣文件的字段信息，包括字段名称、类型、长度等</p>
      </div>

      <Tabs v-model:active-key="activeTabKey">
        <TabPane key="1" tab="字段列表">
          <div class="field-actions">
            <Button type="primary" @click="handleAddField">
              <template #icon><PlusOutlined /></template>
              添加字段
            </Button>
          </div>

          <Table
            :data-source="currentFields"
            :columns="fieldColumns"
            row-key="position"
            :pagination="false"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <!-- 是否必填 -->
              <template v-if="column.key === 'isRequired'">
                <Tag :color="record.isRequired ? 'blue' : 'gray'">
                  {{ record.isRequired ? '必填' : '选填' }}
                </Tag>
              </template>

              <!-- 操作按钮 -->
              <template v-if="column.key === 'operation'">
                <Space>
                  <Button
                    type="primary"
                    size="small"
                    @click="handleEditField(record)"
                  >
                    <template #icon><EditOutlined /></template>
                    编辑
                  </Button>
                  <Popconfirm
                    title="确定要删除该字段吗?"
                    @confirm="handleDeleteField(record.position)"
                    ok-text="确定"
                    cancel-text="取消"
                  >
                    <Button type="primary" danger size="small">
                      <template #icon><DeleteOutlined /></template>
                      删除
                    </Button>
                  </Popconfirm>
                </Space>
              </template>
            </template>
          </Table>

          <div v-if="currentFields.length === 0" class="empty-fields">
            <p>暂无字段配置，请点击"添加字段"按钮添加</p>
          </div>
        </TabPane>

        <TabPane key="2" tab="字段编辑">
          <Form
            ref="fieldFormRef"
            :model="fieldFormState"
            :rules="fieldRules"
            layout="vertical"
          >
            <div class="field-form-grid">
              <FormItem label="字段名称" name="fieldName">
                <Input
                  v-model:value="fieldFormState.fieldName"
                  placeholder="请输入字段名称"
                />
              </FormItem>
              <FormItem label="字段类型" name="fieldType">
                <Select
                  v-model:value="fieldFormState.fieldType"
                  placeholder="请选择字段类型"
                >
                  <Option
                    v-for="option in fieldTypeOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </Option>
                </Select>
              </FormItem>
              <FormItem label="字段长度" name="fieldLength">
                <InputNumber
                  v-model:value="fieldFormState.fieldLength"
                  :min="1"
                  style="width: 100%"
                />
              </FormItem>
              <FormItem label="默认值" name="defaultValue">
                <Input
                  v-model:value="fieldFormState.defaultValue"
                  placeholder="请输入默认值"
                />
              </FormItem>
              <FormItem label="字段位置" name="position">
                <InputNumber
                  v-model:value="fieldFormState.position"
                  :min="1"
                  style="width: 100%"
                />
              </FormItem>
              <FormItem label="是否必填" name="isRequired">
                <Switch v-model:checked="fieldFormState.isRequired" />
              </FormItem>
            </div>

            <FormItem label="描述" name="description">
              <Input.TextArea
                v-model:value="fieldFormState.description"
                placeholder="请输入字段描述"
                :rows="3"
              />
            </FormItem>

            <div class="field-form-actions">
              <Button type="primary" @click="handleSaveField">
                <template #icon><SaveOutlined /></template>
                保存字段
              </Button>
              <Button @click="activeTabKey = '1'"> 取消 </Button>
            </div>
          </Form>
        </TabPane>
      </Tabs>
    </Modal>
  </Page>
</template>

<style scoped>
.auto-debit-container {
  padding: 16px;
}

.header-card {
  margin-bottom: 16px;
}

.header-title {
  margin-bottom: 16px;
}

.header-title h2 {
  margin-bottom: 8px;
}

.header-title p {
  color: rgba(0, 0, 0, 0.45);
  margin: 0;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
}

.rule-list-card {
  margin-bottom: 16px;
}

.field-config-header {
  margin-bottom: 16px;
}

.field-config-header h3 {
  margin-bottom: 8px;
}

.field-config-header p {
  color: rgba(0, 0, 0, 0.45);
  margin: 0;
}

.field-actions {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.field-form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.field-form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.empty-fields {
  text-align: center;
  padding: 32px;
  color: rgba(0, 0, 0, 0.45);
}

.help-text {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}
</style>
