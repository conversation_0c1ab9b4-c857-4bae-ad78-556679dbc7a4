<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  ArrowLeftOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';

import {
  Button,
  Card,
  Descriptions,
  Divider,
  Form,
  Input,
  Radio,
  Select,
  Space,
  Statistic,
  Table,
  Tag,
} from 'ant-design-vue';

import {
  formatBatchType,
  formatStatus,
  getStatusColor,
  mockBatchData,
  mockDebitItems,
} from './managementData';

// 组件
const FormItem = Form.Item;
const { Option } = Select;
const RadioGroup = Radio.Group;

// 路由
const route = useRoute();
const router = useRouter();

// 状态
const batchId = ref('');
const batchInfo = ref(null);
const tableData = ref([]);
const loading = ref(false);
const totalAmount = ref(0);
const successAmount = ref(0);
const failAmount = ref(0);

// 查询参数
const queryParams = reactive({
  userNo: '',
  userName: '',
  status: 'all',
});

// 状态选项
const statusOptions = [
  { label: '全部', value: 'all' },
  { label: '成功', value: 'success' },
  { label: '失败', value: 'fail' },
];

// 表格列配置
const columns = [
  {
    title: '用户编号',
    dataIndex: 'userNo',
    key: 'userNo',
    width: 120,
  },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
  },
  {
    title: '账号',
    dataIndex: 'accountNo',
    key: 'accountNo',
    width: 200,
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '错误信息',
    dataIndex: 'errorMsg',
    key: 'errorMsg',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
];

// 初始化数据
function initData() {
  loading.value = true;

  // 获取批次ID
  batchId.value = route.params.id as string;

  // 模拟获取批次信息
  batchInfo.value = mockBatchData.find((item) => item.id === batchId.value);

  if (!batchInfo.value) {
    loading.value = false;
    return;
  }

  // 模拟获取批次明细
  const items = mockDebitItems.filter((item) => item.batchId === batchId.value);
  tableData.value = [...items];

  // 计算金额统计
  calculateStatistics();

  loading.value = false;
}

// 计算统计数据
function calculateStatistics() {
  totalAmount.value = tableData.value.reduce(
    (sum, item) => sum + item.amount,
    0,
  );
  successAmount.value = tableData.value
    .filter((item) => item.status === 'success')
    .reduce((sum, item) => sum + item.amount, 0);
  failAmount.value = tableData.value
    .filter((item) => item.status === 'fail')
    .reduce((sum, item) => sum + item.amount, 0);
}

// 查询列表
function handleQuery() {
  // 筛选数据
  const filteredData = mockDebitItems
    .filter((item) => item.batchId === batchId.value)
    .filter((item) => {
      if (queryParams.userNo && !item.userNo.includes(queryParams.userNo)) {
        return false;
      }
      if (
        queryParams.userName &&
        !item.userName.includes(queryParams.userName)
      ) {
        return false;
      }
      if (queryParams.status !== 'all' && item.status !== queryParams.status) {
        return false;
      }
      return true;
    });

  tableData.value = [...filteredData];
  calculateStatistics();
}

// 重置查询
function resetQuery() {
  queryParams.userNo = '';
  queryParams.userName = '';
  queryParams.status = 'all';
  handleQuery();
}

// 返回列表
function goBack() {
  router.back();
}

// 导出明细
function exportDetail() {
  if (!batchInfo.value) return;

  const fileName = `${batchInfo.value.batchNo}_明细.xlsx`;
  // 实际应用中应该调用API导出文件
  window.$message.success(`导出明细：${fileName}`);
}

// 初始化
onMounted(() => {
  initData();
});
</script>

<template>
  <Page :auto-content-height="true">
    <div class="debit-detail-container">
      <!-- 头部信息 -->
      <Card class="header-card">
        <div class="header-title">
          <div class="title-with-back">
            <Button type="link" @click="goBack">
              <ArrowLeftOutlined />
            </Button>
            <h2>代扣批次详情</h2>
          </div>
          <div class="header-actions">
            <Button type="primary" @click="exportDetail">
              <template #icon><FileExcelOutlined /></template>
              导出明细
            </Button>
          </div>
        </div>

        <div v-if="batchInfo" class="batch-info">
          <Descriptions
            :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }"
            bordered
          >
            <Descriptions.Item label="批次号">{{
              batchInfo.batchNo
            }}</Descriptions.Item>
            <Descriptions.Item label="银行">{{
              batchInfo.bankName
            }}</Descriptions.Item>
            <Descriptions.Item label="批次类型">{{
              formatBatchType(batchInfo.batchType)
            }}</Descriptions.Item>
            <Descriptions.Item label="账期">{{
              batchInfo.accountPeriod
            }}</Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag :color="getStatusColor(batchInfo.status)">
                {{ formatStatus(batchInfo.status) }}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">{{
              batchInfo.createTime
            }}</Descriptions.Item>
            <Descriptions.Item label="发送时间">{{
              batchInfo.sendTime || '未发送'
            }}</Descriptions.Item>
            <Descriptions.Item label="导入时间">{{
              batchInfo.importTime || '未导入'
            }}</Descriptions.Item>
            <Descriptions.Item label="总笔数">{{
              batchInfo.totalCount
            }}</Descriptions.Item>
            <Descriptions.Item label="成功笔数">{{
              batchInfo.successCount
            }}</Descriptions.Item>
            <Descriptions.Item label="失败笔数">{{
              batchInfo.failCount
            }}</Descriptions.Item>
            <Descriptions.Item label="总金额">{{
              batchInfo.totalAmount.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })
            }}</Descriptions.Item>
            <Descriptions.Item label="文件名">{{
              batchInfo.fileName
            }}</Descriptions.Item>
            <Descriptions.Item label="返盘文件名">{{
              batchInfo.returnFileName || '未导入'
            }}</Descriptions.Item>
            <Descriptions.Item label="备注">{{
              batchInfo.remark
            }}</Descriptions.Item>
          </Descriptions>
        </div>
      </Card>

      <!-- 统计信息 -->
      <div class="statistics-row">
        <Card class="statistic-card">
          <Statistic
            title="总金额"
            :value="totalAmount"
            :precision="2"
            suffix="元"
          />
        </Card>
        <Card class="statistic-card">
          <Statistic
            title="成功金额"
            :value="successAmount"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#3f8600' }"
          />
        </Card>
        <Card class="statistic-card">
          <Statistic
            title="失败金额"
            :value="failAmount"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#cf1322' }"
          />
        </Card>
      </div>

      <!-- 查询区域 -->
      <Card class="query-card">
        <Form layout="inline" :model="queryParams">
          <FormItem label="用户编号">
            <Input
              v-model:value="queryParams.userNo"
              placeholder="输入用户编号"
              style="width: 150px"
              allowClear
            />
          </FormItem>
          <FormItem label="用户姓名">
            <Input
              v-model:value="queryParams.userName"
              placeholder="输入用户姓名"
              style="width: 150px"
              allowClear
            />
          </FormItem>
          <FormItem label="状态">
            <Select v-model:value="queryParams.status" style="width: 120px">
              <Option
                v-for="option in statusOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </Option>
            </Select>
          </FormItem>
          <FormItem>
            <Space>
              <Button type="primary" @click="handleQuery">
                <template #icon><SearchOutlined /></template>
                查询
              </Button>
              <Button @click="resetQuery"> 重置 </Button>
            </Space>
          </FormItem>
        </Form>
      </Card>

      <!-- 明细列表 -->
      <Card title="代扣明细列表" class="detail-list-card">
        <Table
          :data-source="tableData"
          :columns="columns"
          row-key="id"
          :pagination="{
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条记录`,
          }"
          :loading="loading"
          bordered
        >
          <!-- 自定义表格内容 -->
          <template #bodyCell="{ column, record }">
            <!-- 金额 -->
            <template v-if="column.key === 'amount'">
              {{
                record.amount.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
              }}
            </template>

            <!-- 状态 -->
            <template v-if="column.key === 'status'">
              <Tag :color="getStatusColor(record.status)">
                {{ formatStatus(record.status) }}
              </Tag>
            </template>
          </template>
        </Table>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
.debit-detail-container {
  padding: 16px;
}

.header-card {
  margin-bottom: 16px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-with-back {
  display: flex;
  align-items: center;
}

.title-with-back h2 {
  margin: 0;
}

.batch-info {
  margin-top: 16px;
}

.statistics-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.statistic-card {
  flex: 1;
}

.query-card {
  margin-bottom: 16px;
}

.detail-list-card {
  margin-bottom: 16px;
}
</style>
