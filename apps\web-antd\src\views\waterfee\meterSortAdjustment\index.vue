<script setup lang="ts">
import { h, onMounted, onUnmounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Form, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { areaList } from '#/api/waterfee/area/index';
import { getMeterAdjustmentList } from '#/api/waterfee/meterAdjustment';
import { adjustMeterSort } from '#/api/waterfee/meterAdjustment/sort';
import { meterBookList } from '#/api/waterfee/meterbook';
import { listUser } from '#/api/waterfee/user/archivesManage';
import { preserveBigInt } from '#/utils/json-bigint';

// 引入组件
import MeterAdjustmentDrawer from './components/MeterAdjustmentDrawer.vue';
import { columns, querySchema } from './sort.data';
import eventBus, { EventType } from './utils/eventBus';
import { initAllOptions } from './utils/options';

// 表单配置
const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  submitOnChange: true, // 表单值变化时自动提交
};

// 当前选择的表册ID
const currentBookId = ref('');
// 当前选择的区域ID
const currentAreaId = ref('');

// 表格配置
const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  rowConfig: {
    isHover: true,
    // 设置行索引起始值为0，这样序号列显示的值就是从1开始
    // 因为我们在序号列中使用了 rowIndex + 1
    indexMethod: (row) => row._XID,
  },
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        // 获取表册ID
        const bookId = formValues.bookId;
        if (!bookId) {
          return { rows: [], total: 0 };
        }

        // 保存当前表册ID
        currentBookId.value = bookId;

        try {
          // 查询数据
          const resp = await getMeterAdjustmentList(bookId, {
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
          });

          // 检查响应数据格式
          if (resp && resp.rows) {
            // 标准格式，处理数据
            resp.rows = await Promise.all(
              resp.rows.map(async (item) => {
                // 处理营业区域
                let businessAreaName = item.businessAreaName;
                if (!businessAreaName && item.businessAreaId) {
                  try {
                    // 从 API 获取营业区域名称
                    const areaResponse = await areaList();
                    let areaListData = [];

                    // 检查返回值格式
                    if (Array.isArray(areaResponse)) {
                      areaListData = areaResponse;
                    } else if (areaResponse && 'rows' in areaResponse) {
                      areaListData = areaResponse.rows;
                    }

                    console.log('获取到的区域列表:', areaListData);

                    const area = areaListData.find(
                      (a) => a.areaId === item.businessAreaId,
                    );
                    if (area) {
                      businessAreaName = area.areaName;
                    }
                  } catch (error) {
                    console.error('获取营业区域名称失败:', error);
                  }
                }

                // 处理抄表手册
                let meterBookName = item.meterBookName;
                if (!meterBookName && item.meterBookId) {
                  try {
                    // 从 API 获取抄表手册名称
                    const bookInfo = await meterBookList();
                    const book = bookInfo.rows.find(
                      (b) => b.id === item.meterBookId,
                    );
                    if (book) {
                      meterBookName = book.bookName;
                    }
                  } catch (error) {
                    console.error('获取抄表手册名称失败:', error);
                  }
                }

                // 处理用户名
                let userName = item.userName;
                if (!userName && item.userId) {
                  try {
                    // 从 API 获取用户名
                    const userInfo = await listUser();
                    const user = userInfo.rows.find(
                      (u) => u.userId === item.userId,
                    );
                    if (user) {
                      userName = user.userName;
                    }
                  } catch (error) {
                    console.error('获取用户名失败:', error);
                  }
                }

                return {
                  ...item,
                  // 确保这些字段存在，如果不存在则提供默认值
                  businessAreaName: businessAreaName || '未知区域',
                  meterBookName: meterBookName || '未知表册',
                  userName: userName || '未知用户',
                  userNo: item.userNo || '未知编号',
                  communityName: item.communityName || '未知小区',
                  waterNature: item.waterNature || '未知',
                };
              }),
            );
            return resp;
          } else if (resp && Array.isArray(resp)) {
            // 如果响应是数组，转换为标准格式
            console.log('响应是数组，转换为标准格式');
            const processedRows = await Promise.all(
              resp.map(async (item) => {
                // 处理营业区域
                let businessAreaName = item.businessAreaName;
                if (!businessAreaName && item.businessAreaId) {
                  try {
                    // 从 API 获取营业区域名称
                    const areaResponse = await areaList();
                    let areaListData = [];

                    // 检查返回值格式
                    if (Array.isArray(areaResponse)) {
                      areaListData = areaResponse;
                    } else if (areaResponse && 'rows' in areaResponse) {
                      areaListData = areaResponse.rows;
                    }

                    console.log('获取到的区域列表:', areaListData);

                    const area = areaListData.find(
                      (a) => a.areaId === item.businessAreaId,
                    );
                    if (area) {
                      businessAreaName = area.areaName;
                    }
                  } catch (error) {
                    console.error('获取营业区域名称失败:', error);
                  }
                }

                // 处理抄表手册
                let meterBookName = item.meterBookName;
                if (!meterBookName && item.meterBookId) {
                  try {
                    // 从 API 获取抄表手册名称
                    const bookInfo = await meterBookList();
                    const book = bookInfo.rows.find(
                      (b) => b.id === item.meterBookId,
                    );
                    if (book) {
                      meterBookName = book.bookName;
                    }
                  } catch (error) {
                    console.error('获取抄表手册名称失败:', error);
                  }
                }

                // 处理用户名
                let userName = item.userName;
                if (!userName && item.userId) {
                  try {
                    // 从 API 获取用户名
                    const userInfo = await listUser();
                    const user = userInfo.rows.find(
                      (u) => u.userId === item.userId,
                    );
                    if (user) {
                      userName = user.userName;
                    }
                  } catch (error) {
                    console.error('获取用户名失败:', error);
                  }
                }

                return {
                  ...item,
                  // 确保这些字段存在，如果不存在则提供默认值
                  businessAreaName: businessAreaName || '未知区域',
                  meterBookName: meterBookName || '未知表册',
                  userName: userName || '未知用户',
                  userNo: item.userNo || '未知编号',
                  communityName: item.communityName || '未知小区',
                  waterNature: item.waterNature || '未知',
                };
              }),
            );

            return {
              rows: processedRows,
              total: resp.length,
            };
          } else if (resp && resp.code === 200 && resp.data) {
            // 如果响应是 { code: 200, data: [...] } 格式
            console.log(
              '响应是 { code: 200, data: [...] } 格式，转换为标准格式',
            );
            const data = Array.isArray(resp.data) ? resp.data : [resp.data];
            const processedData = await Promise.all(
              data.map(async (item) => {
                // 处理营业区域
                let businessAreaName = item.businessAreaName;
                if (!businessAreaName && item.businessAreaId) {
                  try {
                    // 从 API 获取营业区域名称
                    const areaResponse = await areaList();
                    let areaListData = [];

                    // 检查返回值格式
                    if (Array.isArray(areaResponse)) {
                      areaListData = areaResponse;
                    } else if (areaResponse && 'rows' in areaResponse) {
                      areaListData = areaResponse.rows;
                    }

                    console.log('获取到的区域列表:', areaListData);

                    const area = areaListData.find(
                      (a) => a.areaId === item.businessAreaId,
                    );
                    if (area) {
                      businessAreaName = area.areaName;
                    }
                  } catch (error) {
                    console.error('获取营业区域名称失败:', error);
                  }
                }

                // 处理抄表手册
                let meterBookName = item.meterBookName;
                if (!meterBookName && item.meterBookId) {
                  try {
                    // 从 API 获取抄表手册名称
                    const bookInfo = await meterBookList();
                    const book = bookInfo.rows.find(
                      (b) => b.id === item.meterBookId,
                    );
                    if (book) {
                      meterBookName = book.bookName;
                    }
                  } catch (error) {
                    console.error('获取抄表手册名称失败:', error);
                  }
                }

                // 处理用户名
                let userName = item.userName;
                if (!userName && item.userId) {
                  try {
                    // 从 API 获取用户名
                    const userInfo = await listUser();
                    const user = userInfo.rows.find(
                      (u) => u.userId === item.userId,
                    );
                    if (user) {
                      userName = user.userName;
                    }
                  } catch (error) {
                    console.error('获取用户名失败:', error);
                  }
                }

                return {
                  ...item,
                  // 确保这些字段存在，如果不存在则提供默认值
                  businessAreaName: businessAreaName || '未知区域',
                  meterBookName: meterBookName || '未知表册',
                  userName: userName || '未知用户',
                  userNo: item.userNo || '未知编号',
                  communityName: item.communityName || '未知小区',
                  waterNature: item.waterNature || '未知',
                };
              }),
            );

            return {
              rows: processedData,
              total: data.length,
            };
          }

          // 如果没有匹配的格式，返回原始响应
          return resp;
        } catch (error) {
          console.error('获取册本调整列表失败:', error);
          message.error(`获取数据失败：${error.message || '未知错误'}`);
          return { rows: [], total: 0 };
        }
      },
    },
  },
  id: 'waterfee-meter-adjustment-index',
};

// 初始化表格
const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 抽屉状态
const drawerOpen = ref(false);
const selectedMeterIds = ref([]);

// 批量调整水表所属区域和表册
// function handleBatchAdjust() {
//   try {
//     // 获取选中的行
//     const selectedRows = tableApi.grid.getCheckboxRecords();

//     if (!selectedRows || selectedRows.length === 0) {
//       message.warning('请选择要调整的水表');
//       return;
//     }

//     console.log('选中的行:', selectedRows);

//     // 确保ID不会丢失精度
//     const meterIds = selectedRows.map((row) => {
//       // 检查行数据格式
//       if (!row.meterId) {
//         console.warn('行数据中没有 meterId 字段:', row);
//         // 尝试从其他可能的字段中获取 ID
//         const possibleIdFields = ['id', 'ID', 'meter_id', 'METER_ID'];
//         for (const field of possibleIdFields) {
//           if (row[field]) {
//             console.log(`使用 ${field} 字段作为 meterId:`, row[field]);
//             return String(row[field]);
//           }
//         }
//         // 如果没有找到 ID，使用一个唯一标识符
//         return String(Date.now() + Math.random());
//       }

//       const safeRecord = preserveBigInt(row);
//       return String(safeRecord.meterId);
//     });

//     // 设置选中的水表IDs
//     selectedMeterIds.value = meterIds;

//     // 获取第一个选中行的区域ID
//     currentAreaId.value =
//       selectedRows.length > 0 && selectedRows[0].businessAreaId
//         ? selectedRows[0].businessAreaId
//         : '';

//     console.log('当前区域ID:', currentAreaId.value);
//     console.log('当前表册ID:', currentBookId.value);

//     // 打开抽屉
//     drawerOpen.value = true;
//   } catch (error) {
//     message.error(`操作失败：${error.message || '未知错误'}`);
//   }
// }

// 操作成功后刷新表格
function handleSuccess() {
  tableApi.query();
}

// 监听表单提交事件
function handleFormSubmit(formValues) {
  console.log('表单提交:', formValues);
  // 如果表册ID变化，重新查询数据
  if (formValues && formValues.bookId !== currentBookId.value) {
    tableApi.query();
  }
}

// 处理单个水表调整
function handleAdjustMeter(row) {
  console.log('处理单个水表调整:', row);

  // 确保ID不会丢失精度
  let meterId;
  if (row.meterId) {
    const safeRecord = preserveBigInt(row);
    meterId = String(safeRecord.meterId);
  } else {
    console.warn('行数据中没有 meterId 字段:', row);
    // 尝试从其他可能的字段中获取 ID
    const possibleIdFields = ['id', 'ID', 'meter_id', 'METER_ID'];
    for (const field of possibleIdFields) {
      if (row[field]) {
        console.log(`使用 ${field} 字段作为 meterId:`, row[field]);
        meterId = String(row[field]);
        break;
      }
    }
    // 如果没有找到 ID，使用一个唯一标识符
    if (!meterId) {
      meterId = String(Date.now() + Math.random());
    }
  }

  // 设置选中的水表IDs
  selectedMeterIds.value = [meterId];

  // 获取区域ID
  currentAreaId.value = row.businessAreaId || '';

  console.log('当前区域ID:', currentAreaId.value);
  console.log('当前表册ID:', currentBookId.value);

  // 打开抽屉
  drawerOpen.value = true;
}

// 处理水表上移
async function handleMoveUp(row) {
  console.log('处理水表上移:', row);

  try {
    // 确保ID不会丢失精度
    let meterId;
    if (row.meterId) {
      const safeRecord = preserveBigInt(row);
      meterId = String(safeRecord.meterId);
    } else {
      console.warn('行数据中没有 meterId 字段:', row);
      // 尝试从其他可能的字段中获取 ID
      const possibleIdFields = ['id', 'ID', 'meter_id', 'METER_ID'];
      for (const field of possibleIdFields) {
        if (row[field]) {
          console.log(`使用 ${field} 字段作为 meterId:`, row[field]);
          meterId = String(row[field]);
          break;
        }
      }
      // 如果没有找到 ID，使用一个唯一标识符
      if (!meterId) {
        meterId = String(Date.now() + Math.random());
      }
    }

    // 获取当前序号
    const currentSortNo =
      row.sortNo !== undefined && row.sortNo !== null ? row.sortNo : 0;
    console.log('当前序号:', currentSortNo);

    // 获取表格中的所有行数据
    const allRows = tableApi.grid.getData();
    console.log('表格中的所有行数据:', allRows);

    // 找出当前行的索引
    const currentIndex = allRows.findIndex((item) => {
      const itemId = item.meterId ? String(item.meterId) : '';
      return itemId === meterId;
    });
    console.log('当前行的索引:', currentIndex);

    if (currentIndex === -1) {
      message.error('找不到当前水表在表格中的位置');
      return;
    }

    // 找出上一行的序号（如果存在）
    let prevSortNo = null;
    if (currentIndex > 0) {
      const prevRow = allRows[currentIndex - 1];
      prevSortNo =
        prevRow.sortNo !== undefined && prevRow.sortNo !== null
          ? prevRow.sortNo
          : 0;
      console.log('上一行的序号:', prevSortNo);
    }

    // 计算新序号
    let newSortNo;
    if (prevSortNo === null) {
      // 如果不存在上一行（当前行是第一行），则新序号为当前序号减1
      newSortNo = currentSortNo - 1;
    } else {
      // 如果存在上一行，则新序号为上一行序号和当前序号的中间值
      // 如果上一行序号大于或等于当前序号，则新序号为上一行序号减1
      newSortNo = prevSortNo >= currentSortNo ? prevSortNo - 1 : prevSortNo;
    }

    // 调用API进行上移操作
    await adjustMeterSort({
      meterId,
      bookId: currentBookId.value,
      operationType: 'up',
      meterSortItems: [{ meterId, sortNo: newSortNo }], // 使用新序号
    });

    message.success('水表上移成功');
    // 刷新表格
    tableApi.query();
  } catch (error) {
    console.error('水表上移失败:', error);
    message.error(`操作失败：${error.message || '未知错误'}`);
  }
}

// 处理水表下移
async function handleMoveDown(row) {
  console.log('处理水表下移:', row);

  try {
    // 确保ID不会丢失精度
    let meterId;
    if (row.meterId) {
      const safeRecord = preserveBigInt(row);
      meterId = String(safeRecord.meterId);
    } else {
      console.warn('行数据中没有 meterId 字段:', row);
      // 尝试从其他可能的字段中获取 ID
      const possibleIdFields = ['id', 'ID', 'meter_id', 'METER_ID'];
      for (const field of possibleIdFields) {
        if (row[field]) {
          console.log(`使用 ${field} 字段作为 meterId:`, row[field]);
          meterId = String(row[field]);
          break;
        }
      }
      // 如果没有找到 ID，使用一个唯一标识符
      if (!meterId) {
        meterId = String(Date.now() + Math.random());
      }
    }

    // 获取当前序号
    const currentSortNo =
      row.sortNo !== undefined && row.sortNo !== null ? row.sortNo : 0;
    console.log('当前序号:', currentSortNo);

    // 获取表格中的所有行数据
    const allRows = tableApi.grid.getData();
    console.log('表格中的所有行数据:', allRows);

    // 找出当前行的索引
    const currentIndex = allRows.findIndex((item) => {
      const itemId = item.meterId ? String(item.meterId) : '';
      return itemId === meterId;
    });
    console.log('当前行的索引:', currentIndex);

    if (currentIndex === -1) {
      message.error('找不到当前水表在表格中的位置');
      return;
    }

    // 找出下一行的序号（如果存在）
    let nextSortNo = null;
    if (currentIndex < allRows.length - 1) {
      const nextRow = allRows[currentIndex + 1];
      nextSortNo =
        nextRow.sortNo !== undefined && nextRow.sortNo !== null
          ? nextRow.sortNo
          : 0;
      console.log('下一行的序号:', nextSortNo);
    }

    // 计算新序号
    let newSortNo;
    if (nextSortNo === null) {
      // 如果不存在下一行（当前行是最后一行），则新序号为当前序号加1
      newSortNo = currentSortNo + 1;
    } else {
      // 如果存在下一行，则新序号为下一行序号和当前序号的中间值
      // 如果下一行序号小于或等于当前序号，则新序号为下一行序号加1
      newSortNo = nextSortNo <= currentSortNo ? nextSortNo + 1 : nextSortNo;
    }

    // 调用API进行下移操作
    await adjustMeterSort({
      meterId,
      bookId: currentBookId.value,
      operationType: 'down',
      meterSortItems: [{ meterId, sortNo: newSortNo }], // 使用新序号
    });

    message.success('水表下移成功');
    // 刷新表格
    tableApi.query();
  } catch (error) {
    console.error('水表下移失败:', error);
    message.error(`操作失败：${error.message || '未知错误'}`);
  }
}

// 自定义序号表单
const customSortForm = reactive({
  sortNo: 0,
  error: '', // 添加错误信息字段
});

// 处理修改水表序号
function handleCustomSort(row) {
  console.log('处理修改水表序号:', row);

  // 确保ID不会丢失精度
  let meterId;
  if (row.meterId) {
    const safeRecord = preserveBigInt(row);
    meterId = String(safeRecord.meterId);
  } else {
    console.warn('行数据中没有 meterId 字段:', row);
    // 尝试从其他可能的字段中获取 ID
    const possibleIdFields = ['id', 'ID', 'meter_id', 'METER_ID'];
    for (const field of possibleIdFields) {
      if (row[field]) {
        console.log(`使用 ${field} 字段作为 meterId:`, row[field]);
        meterId = String(row[field]);
        break;
      }
    }
    // 如果没有找到 ID，使用一个唯一标识符
    if (!meterId) {
      meterId = String(Date.now() + Math.random());
    }
  }

  // 设置默认序号
  customSortForm.sortNo =
    row.sortNo !== undefined && row.sortNo !== null ? row.sortNo : 0;
  customSortForm.error = ''; // 清除错误信息
  console.log('初始序号值:', customSortForm.sortNo);

  // 显示修改序号对话框
  Modal.confirm({
    title: '修改水表序号',
    content: h('div', [
      h(
        Form,
        { layout: 'vertical' },
        {
          default: () => [
            h(
              Form.Item,
              {
                label: '序号',
                validateStatus: customSortForm.error ? 'error' : '',
                help: customSortForm.error,
              },
              {
                default: () => [
                  // 使用普通的 input 元素，只接受整数输入
                  h('input', {
                    type: 'text',
                    value: customSortForm.sortNo,
                    style: {
                      width: '100%',
                      padding: '4px 11px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '2px',
                      fontSize: '14px',
                      lineHeight: '1.5715',
                    },
                    onInput: (e) => {
                      const inputValue = e.target.value;
                      console.log('输入值:', inputValue);

                      // 允许输入整数（可以包含负号）或者单独的负号（正在输入负数的过程中）
                      const integerRegex = /^-?\d*$/;

                      if (inputValue === '' || inputValue === '-') {
                        // 允许空输入或单独的负号，设置为默认值0或保持当前值
                        if (inputValue === '-') {
                          // 如果只输入了负号，暂时不更新值，但清除错误信息
                          customSortForm.error = ''; // 清除错误信息
                        } else {
                          // 空输入，设置为默认值0
                          customSortForm.sortNo = 0;
                          customSortForm.error = ''; // 清除错误信息
                        }
                      } else if (integerRegex.test(inputValue)) {
                        // 输入是整数格式，转换为数字
                        const numValue = Number.parseInt(inputValue, 10);
                        customSortForm.sortNo = numValue;
                        customSortForm.error = ''; // 清除错误信息
                      } else {
                        // 如果输入不是整数格式，显示错误信息
                        customSortForm.error = '请输入整数';
                        // 保留当前值，不更新
                      }

                      console.log(
                        '设置后的序号值:',
                        customSortForm.sortNo,
                        '错误信息:',
                        customSortForm.error,
                      );
                    },
                  }),
                  h('div', {
                    style: {
                      marginTop: '10px',
                      display: 'flex',
                      justifyContent: 'space-between',
                    },
                  }),
                ],
              },
            ),
          ],
        },
      ),
    ]),
    async onOk() {
      try {
        // 获取用户输入的序号
        const userInputSortNo = customSortForm.sortNo;
        console.log('用户输入的序号:', userInputSortNo);

        // 验证输入是否有效
        if (customSortForm.error) {
          message.error('请修正输入错误后再提交');
          throw new Error('输入错误'); // 阻止对话框关闭
        }

        if (!Number.isInteger(userInputSortNo)) {
          message.error('序号必须是整数');
          customSortForm.error = '序号必须是整数';
          throw new Error('序号必须是整数'); // 阻止对话框关闭
        }

        // 调用API进行自定义序号操作
        await adjustMeterSort({
          meterId,
          bookId: currentBookId.value,
          operationType: 'custom',
          customSortNo: userInputSortNo,
          meterSortItems: [{ meterId, sortNo: userInputSortNo }], // 添加 MeterSortItem 对象，确保序号一致
        });

        message.success('水表序号修改成功');
        // 刷新表格
        tableApi.query();
      } catch (error) {
        console.error('水表序号修改失败:', error);
        message.error(`操作失败：${error.message || '未知错误'}`);
      }
    },
  });
}

// 组件挂载时初始化所有选项
onMounted(async () => {
  try {
    await initAllOptions();
    console.log('册本调整页面选项初始化完成');

    // 添加事件监听器
    eventBus.on(EventType.ADJUST_METER, handleAdjustMeter);
    eventBus.on(EventType.MOVE_UP, handleMoveUp);
    eventBus.on(EventType.MOVE_DOWN, handleMoveDown);
    eventBus.on(EventType.CUSTOM_SORT, handleCustomSort);
  } catch (error) {
    console.error('初始化选项失败:', error);
    message.error('加载选项数据失败，请刷新页面重试');
  }
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  eventBus.off(EventType.ADJUST_METER, handleAdjustMeter);
  eventBus.off(EventType.MOVE_UP, handleMoveUp);
  eventBus.off(EventType.MOVE_DOWN, handleMoveDown);
  eventBus.off(EventType.CUSTOM_SORT, handleCustomSort);
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable
      @reload="tableApi.query()"
      @form-submit="handleFormSubmit"
      table-title="册内调整"
    />

    <!-- 册本调整抽屉 -->
    <MeterAdjustmentDrawer
      :open="drawerOpen"
      @update:open="drawerOpen = $event"
      :selected-meter-ids="selectedMeterIds"
      :current-book-id="currentBookId"
      :current-area-id="currentAreaId"
      @reload="handleSuccess"
    />
  </Page>
</template>

<style scoped>
/* 自定义样式 */
</style>
