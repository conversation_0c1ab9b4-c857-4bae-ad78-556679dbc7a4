/**
 * @description 代扣批次状态选项
 */
export const statusOptions = [
  { label: '全部状态', value: 'all' },
  { label: '待发送', value: 'pending' },
  { label: '已发送', value: 'sent' },
  { label: '处理中', value: 'processing' },
  { label: '已完成', value: 'completed' },
  { label: '异常', value: 'error' },
];

/**
 * @description 批次类型选项
 */
export const batchTypeOptions = [
  { label: '全部类型', value: 'all' },
  { label: '常规代扣', value: 'regular' },
  { label: '一次性代扣', value: 'oneTime' },
  { label: '欠费补缴', value: 'arrears' },
];

/**
 * @description 模拟代扣批次数据
 */
export const mockBatchData = [
  {
    id: 'batch_001',
    batchNo: 'DK20230501001',
    bankId: 'ICBC',
    bankName: '中国工商银行',
    batchType: 'regular',
    accountPeriod: '2023年05月',
    createTime: '2023-05-01 09:15:30',
    sendTime: '2023-05-01 10:20:45',
    importTime: '2023-05-03 14:30:22',
    totalCount: 2568,
    successCount: 2510,
    failCount: 58,
    totalAmount: 385621.5,
    status: 'completed',
    fileName: 'ICBC_20230501.txt',
    returnFileName: 'ICBC_RETURN_20230503.txt',
    remark: '5月常规代扣',
  },
  {
    id: 'batch_002',
    batchNo: 'DK20230601001',
    bankId: 'CCB',
    bankName: '中国建设银行',
    batchType: 'regular',
    accountPeriod: '2023年06月',
    createTime: '2023-06-01 08:45:20',
    sendTime: '2023-06-01 09:55:35',
    importTime: null,
    totalCount: 3120,
    successCount: 0,
    failCount: 0,
    totalAmount: 456890.75,
    status: 'sent',
    fileName: 'CCB_20230601.txt',
    returnFileName: null,
    remark: '6月常规代扣',
  },
  {
    id: 'batch_003',
    batchNo: 'DK20230615001',
    bankId: 'ABC',
    bankName: '中国农业银行',
    batchType: 'arrears',
    accountPeriod: '2023年06月',
    createTime: '2023-06-15 13:25:10',
    sendTime: null,
    importTime: null,
    totalCount: 658,
    successCount: 0,
    failCount: 0,
    totalAmount: 89542.25,
    status: 'pending',
    fileName: 'ABC_20230615.txt',
    returnFileName: null,
    remark: '欠费补缴批次',
  },
  {
    id: 'batch_004',
    batchNo: 'DK20230701001',
    bankId: 'BOC',
    bankName: '中国银行',
    batchType: 'regular',
    accountPeriod: '2023年07月',
    createTime: '2023-07-01 08:30:40',
    sendTime: '2023-07-01 10:15:25',
    importTime: '2023-07-04 09:45:30',
    totalCount: 1856,
    successCount: 1810,
    failCount: 46,
    totalAmount: 276890.5,
    status: 'completed',
    fileName: 'BOC_20230701.xlsx',
    returnFileName: 'BOC_RETURN_20230704.xlsx',
    remark: '7月常规代扣',
  },
  {
    id: 'batch_005',
    batchNo: 'DK20230720001',
    bankId: 'ICBC',
    bankName: '中国工商银行',
    batchType: 'oneTime',
    accountPeriod: '2023年07月',
    createTime: '2023-07-20 14:10:35',
    sendTime: '2023-07-20 16:05:50',
    importTime: null,
    totalCount: 185,
    successCount: 0,
    failCount: 0,
    totalAmount: 35260.75,
    status: 'processing',
    fileName: 'ICBC_20230720.txt',
    returnFileName: null,
    remark: '一次性代扣批次',
  },
  {
    id: 'batch_006',
    batchNo: 'DK20230801001',
    bankId: 'COMM',
    bankName: '交通银行',
    batchType: 'regular',
    accountPeriod: '2023年08月',
    createTime: '2023-08-01 08:55:15',
    sendTime: '2023-08-01 10:30:40',
    importTime: '2023-08-04 11:20:35',
    totalCount: 1325,
    successCount: 1290,
    failCount: 35,
    totalAmount: 196780.25,
    status: 'completed',
    fileName: 'COMM_20230801.txt',
    returnFileName: 'COMM_RETURN_20230804.txt',
    remark: '8月常规代扣',
  },
  {
    id: 'batch_007',
    batchNo: 'DK20230810001',
    bankId: 'CCB',
    bankName: '中国建设银行',
    batchType: 'arrears',
    accountPeriod: '2023年08月',
    createTime: '2023-08-10 13:40:25',
    sendTime: '2023-08-10 15:25:40',
    importTime: null,
    totalCount: 458,
    successCount: 0,
    failCount: 0,
    totalAmount: 68950.5,
    status: 'sent',
    fileName: 'CCB_20230810.txt',
    returnFileName: null,
    remark: '欠费补缴批次',
  },
  {
    id: 'batch_008',
    batchNo: 'DK20230901001',
    bankId: 'ICBC',
    bankName: '中国工商银行',
    batchType: 'regular',
    accountPeriod: '2023年09月',
    createTime: '2023-09-01 09:05:30',
    sendTime: '2023-09-01 11:15:45',
    importTime: '2023-09-04 10:30:20',
    totalCount: 2780,
    successCount: 2710,
    failCount: 70,
    totalAmount: 415680.75,
    status: 'completed',
    fileName: 'ICBC_20230901.txt',
    returnFileName: 'ICBC_RETURN_20230904.txt',
    remark: '9月常规代扣',
  },
  {
    id: 'batch_009',
    batchNo: 'DK20230915001',
    bankId: 'ABC',
    bankName: '中国农业银行',
    batchType: 'oneTime',
    accountPeriod: '2023年09月',
    createTime: '2023-09-15 14:20:10',
    sendTime: null,
    importTime: null,
    totalCount: 125,
    successCount: 0,
    failCount: 0,
    totalAmount: 28760.3,
    status: 'pending',
    fileName: 'ABC_20230915.txt',
    returnFileName: null,
    remark: '一次性代扣批次',
  },
  {
    id: 'batch_010',
    batchNo: 'DK20231001001',
    bankId: 'BOC',
    bankName: '中国银行',
    batchType: 'regular',
    accountPeriod: '2023年10月',
    createTime: '2023-10-01 08:40:50',
    sendTime: '2023-10-01 10:25:35',
    importTime: '2023-10-04 09:15:40',
    totalCount: 1950,
    successCount: 1920,
    failCount: 30,
    totalAmount: 289570.5,
    status: 'completed',
    fileName: 'BOC_20231001.xlsx',
    returnFileName: 'BOC_RETURN_20231004.xlsx',
    remark: '10月常规代扣',
  },
];

/**
 * @description 模拟代扣明细数据
 */
export const mockDebitItems = [
  {
    id: 'item_001',
    batchId: 'batch_001',
    userNo: '10001',
    userName: '张三',
    accountNo: '6222021234567890123',
    amount: 158.5,
    status: 'success',
    errorMsg: null,
    createTime: '2023-05-01 09:15:30',
  },
  {
    id: 'item_002',
    batchId: 'batch_001',
    userNo: '10002',
    userName: '李四',
    accountNo: '6222021234567890124',
    amount: 125.0,
    status: 'success',
    errorMsg: null,
    createTime: '2023-05-01 09:15:30',
  },
  {
    id: 'item_003',
    batchId: 'batch_001',
    userNo: '10003',
    userName: '王五',
    accountNo: '6222021234567890125',
    amount: 210.75,
    status: 'fail',
    errorMsg: '账户余额不足',
    createTime: '2023-05-01 09:15:30',
  },
  {
    id: 'item_004',
    batchId: 'batch_002',
    userNo: '10004',
    userName: '赵六',
    accountNo: '6222021234567890126',
    amount: 180.25,
    status: 'pending',
    errorMsg: null,
    createTime: '2023-06-01 08:45:20',
  },
  {
    id: 'item_005',
    batchId: 'batch_002',
    userNo: '10005',
    userName: '钱七',
    accountNo: '6222021234567890127',
    amount: 145.5,
    status: 'pending',
    errorMsg: null,
    createTime: '2023-06-01 08:45:20',
  },
];

/**
 * @description 格式化批次类型
 */
export function formatBatchType(type: string): string {
  const map = {
    regular: '常规代扣',
    oneTime: '一次性代扣',
    arrears: '欠费补缴',
  };
  return map[type] || type;
}

/**
 * @description 格式化状态
 */
export function formatStatus(status: string): string {
  const map = {
    pending: '待发送',
    sent: '已发送',
    processing: '处理中',
    completed: '已完成',
    error: '异常',
    success: '成功',
    fail: '失败',
  };
  return map[status] || status;
}

/**
 * @description 获取状态标签颜色
 */
export function getStatusColor(status: string): string {
  const map = {
    pending: 'blue',
    sent: 'orange',
    processing: 'gold',
    completed: 'green',
    error: 'red',
    success: 'green',
    fail: 'red',
  };
  return map[status] || 'default';
}
