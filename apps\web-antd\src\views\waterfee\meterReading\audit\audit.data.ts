import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import { CheckOutlined, EyeOutlined } from '@ant-design/icons-vue';
import { Button, Space, Tag } from 'ant-design-vue';
// 导入InputNumber组件
import { InputNumber } from 'ant-design-vue';

import { getDictOptions as getSystemDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

import eventBus, { EventType } from './utils/eventBus';
import { meterBookOptions } from './utils/options';

// 查询表单架构
export function querySchema() {
  return [
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择表册',
        options: meterBookOptions,
        allowClear: true,
        onChange: (value: string | undefined) => {
          // 选择表册后手动触发查询
          console.log('表册选择变更:', value);
          // 使用事件总线触发查询
          eventBus.emit(EventType.FORM_SUBMIT, { meterBookId: value });
        },
        // 添加防抖处理
        onSearch: (value: string) => {
          console.log('表册搜索:', value);
        },
      },
      fieldName: 'meterBookId',
      label: '表册',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择抄表方式',
        options: getDictOptions('waterfee_reading_method'),
        allowClear: true,
      },
      fieldName: 'readingMethod',
      label: '抄表方式',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择水表类型',
        options: getDictOptions('waterfee_meter_type'),
        allowClear: true,
      },
      fieldName: 'meterType',
      label: '水表类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择审核状态',
        options: [
          { label: '未审核', value: '0' },
          { label: '已审核', value: '1' },
        ],
        allowClear: true,
      },
      fieldName: 'isAudited',
      label: '审核状态',
    },
  ];
}

// 获取字典选项
function getDictOptions(dictType: string) {
  // 使用系统的字典选项函数
  return getSystemDictOptions(dictType);
}

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
    fixed: 'left',
    align: 'center',
    // 使用自定义渲染函数来显示序号
    slots: {
      default: ({ rowIndex }) => {
        return h('span', {}, rowIndex + 1);
      },
    },
  },
  {
    field: 'businessAreaName',
    title: '营业区域',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'meterBookName',
    title: '抄表手册',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'readerName',
    title: '抄表员',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'readingMethod',
    title: '抄表方式',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return h(
          'div',
          {},
          renderDict(row.readingMethod, 'waterfee_reading_method'),
        );
      },
    },
  },
  {
    field: 'readingDay',
    title: '抄表例日',
    minWidth: 80,
    align: 'center',
  },
  {
    field: 'baseDay',
    title: '抄表基准日',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'bookUserNum',
    title: '手册用户数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'planReadingNum',
    title: '计划抄表数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'actualReadingNum',
    title: '实际抄表数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'readingMonth',
    title: '抄表月份',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'isAudited',
    title: '审核状态',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        const isAudited = row.isAudited === '1';
        return h(
          Tag,
          {
            color: isAudited ? 'success' : 'warning',
          },
          { default: () => (isAudited ? '已审核' : '未审核') },
        );
      },
    },
  },
  {
    field: 'auditTime',
    title: '审核时间',
    minWidth: 150,
    align: 'center',
  },
  {
    field: 'auditorName',
    title: '审核人',
    minWidth: 100,
    align: 'center',
  },
  {
    title: '操作',
    width: 150,
    fixed: 'right',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return h('div', [
          h(
            Space,
            {},
            {
              default: () => [
                h(
                  Button,
                  {
                    type: 'link',
                    size: 'small',
                    onClick: () => {
                      console.log('查看详情:', row);
                      // 通过事件总线触发查看详情事件
                      eventBus.emit(EventType.VIEW_DETAILS, row);
                    },
                  },
                  {
                    default: () => '详情',
                    icon: () => h(EyeOutlined),
                  },
                ),
                // 添加审核按钮，仅在未审核状态下显示
                row.isAudited === '1'
                  ? null
                  : h(
                      Button,
                      {
                        type: 'link',
                        size: 'small',
                        onClick: () => {
                          console.log('审核记录:', row);
                          // 通过事件总线触发审核事件
                          eventBus.emit(EventType.AUDIT_RECORDS, row);
                        },
                      },
                      {
                        default: () => '审核',
                        icon: () => h(CheckOutlined),
                      },
                    ),
              ],
            },
          ),
        ]);
      },
    },
  },
];

// 抄表记录详情表格列配置
export const recordColumns: VxeGridProps['columns'] = [
  {
    type: 'seq',
    title: '序号',
    width: 70,
    fixed: 'left',
    align: 'center',
    // 使用自定义渲染函数来显示序号
    slots: {
      default: ({ rowIndex }) => {
        return h('span', {}, rowIndex + 1);
      },
    },
  },
  {
    field: 'meterNo',
    title: '水表编号',
    minWidth: 140,
    align: 'center',
  },
  {
    field: 'userNo',
    title: '用户编号',
    minWidth: 140,
    align: 'center',
  },
  {
    field: 'userName',
    title: '用户名',
    minWidth: 140,
    align: 'center',
  },
  {
    field: 'lastReading',
    title: '上期抄表读数',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'lastReadingTime',
    title: '上期抄表时间',
    minWidth: 180,
    align: 'center',
  },
  {
    field: 'currentReading',
    title: '本期抄表读数',
    minWidth: 120,
    align: 'center',
    editRender: {},
    slots: {
      edit: ({ row }) => {
        return h(InputNumber, {
          value: row.currentReading,
          min: 0,
          precision: 2,
          style: { width: '100%' },
          disabled: row.isAudited === '1' || row.isPending === '1', // 已审核或挂起时禁用编辑
          onChange: (value) => {
            row.currentReading = value;
            // 重新计算水量
            row.waterUsage =
              (row.oldMeterStopReading || 0) +
              (value || 0) -
              (row.lastReading || 0);
          },
        });
      },
      default: ({ row }) => {
        // 为已审核的记录添加视觉提示
        if (row.isAudited === '1' || row.isPending === '1') {
          return h(
            'span',
            {
              style: {
                color: '#666',
                fontWeight: 'normal',
              },
            },
            row.currentReading || '-',
          );
        }
        return h('span', {}, row.currentReading || '-');
      },
    },
  },
  {
    field: 'waterUsage',
    title: '本期水量',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'oldMeterStopReading',
    title: '旧表止数',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'readingTime',
    title: '本期抄表时间',
    minWidth: 180,
    align: 'center',
  },
  {
    field: 'isPending',
    title: '挂起状态',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        const isPending = row.isPending === '1';
        return isPending
          ? h(
              Tag,
              {
                color: 'error',
              },
              { default: () => '已挂起' },
            )
          : null;
      },
    },
  },
  {
    field: 'pendingReason',
    title: '挂起原因',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return row.isPending === '1'
          ? h('span', {}, row.pendingReason || '-')
          : null;
      },
    },
  },
  {
    field: 'isAudited',
    title: '审核状态',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        const isAudited = row.isAudited === '1';
        return h(
          Tag,
          {
            color: isAudited ? 'success' : 'warning',
          },
          { default: () => (isAudited ? '已审核' : '未审核') },
        );
      },
    },
  },
];
