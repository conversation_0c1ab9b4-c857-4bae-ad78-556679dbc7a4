import { requestClient } from '#/api/request';

// 抄表记录API
enum Api {
  auditRecord = '/waterfee/meterReadingRecord/audit', // 审核抄表记录
  auditRecordsByBook = '/waterfee/meterReadingRecord/audit/book', // 按表册审核抄表记录
  batchCancelPendingRecord = '/waterfee/meterReadingRecord/batchCancelPending', // 批量取消挂起抄表记录
  batchPendingRecord = '/waterfee/meterReadingRecord/batchPending', // 批量挂起抄表记录
  cancelPendingRecord = '/waterfee/meterReadingRecord/cancelPending', // 取消挂起抄表记录
  historyReading = '/waterfee/meterReadingRecord/history', // 获取抄表历史记录
  latestReading = '/waterfee/meterReadingRecord/latest', // 获取最新抄表记录
  pendingList = '/waterfee/meterReadingRecord/pendingList', // 查询挂起状态的抄表记录列表
  pendingRecord = '/waterfee/meterReadingRecord/pending', // 挂起抄表记录
}

// 最新抄表记录模型
export interface LatestReadingModel {
  lastReading?: number; // 上期读数
  oldMeterStopReading?: number; // 旧表止数
  readingTime?: string; // 抄表时间
  [key: string]: any;
}

// 获取最新抄表记录
export function getLatestReading(meterNo: string) {
  return requestClient.get<LatestReadingModel>(
    `${Api.latestReading}/${meterNo}`,
  );
}

// 历史抄表记录模型
export interface HistoryReadingModel {
  id?: string;
  lastReading?: number; // 上期抄表读数
  lastReadingTime?: string; // 上期抄表时间
  currentReading?: number; // 本期抄表读数
  readingTime?: string; // 本期抄表时间
  oldMeterStopReading?: number; // 旧表止数
  waterUsage?: number; // 本期水量
  [key: string]: any;
}

// 获取历史抄表记录
export function getHistoryReadings(meterNo: string) {
  return requestClient.get<HistoryReadingModel[]>(
    `${Api.historyReading}/${meterNo}`,
  );
}

/**
 * 审核单条抄表记录
 * @param recordId 抄表记录ID
 * @returns Promise<void>
 */
export function auditRecord(recordId: number | string) {
  return requestClient.put<void>(`${Api.auditRecord}/${recordId}`);
}

/**
 * 按表册审核抄表记录
 * @param meterBookId 表册ID
 * @param taskId 任务ID
 * @returns Promise<void>
 */
export function auditRecordsByBook(
  meterBookId: number | string,
  taskId: number | string,
) {
  return requestClient.put<void>(Api.auditRecordsByBook, null, {
    params: {
      meterBookId,
      taskId,
    },
  });
}

/**
 * 挂起抄表记录
 * @param recordId 抄表记录ID
 * @param reason 挂起原因
 * @returns Promise<void>
 */
export function pendingRecord(recordId: number | string, reason: string) {
  return requestClient.put<void>(`${Api.pendingRecord}/${recordId}`, null, {
    params: {
      reason,
    },
  });
}

/**
 * 取消挂起抄表记录
 * @param recordId 抄表记录ID
 * @returns Promise<void>
 */
export function cancelPendingRecord(recordId: number | string) {
  return requestClient.put<void>(`${Api.cancelPendingRecord}/${recordId}`);
}

/**
 * 批量挂起抄表记录
 * @param recordIds 抄表记录ID数组
 * @param reason 挂起原因
 * @returns Promise<void>
 */
export function batchPendingRecord(
  recordIds: (number | string)[],
  reason: string,
) {
  return requestClient.put<void>(Api.batchPendingRecord, {
    recordIds,
    reason,
    _t: Date.now(),
  });
}

/**
 * 批量取消挂起抄表记录
 * @param recordIds 抄表记录ID数组
 * @returns Promise<void>
 */
export function batchCancelPendingRecord(recordIds: (number | string)[]) {
  return requestClient.put<void>(Api.batchCancelPendingRecord, {
    recordIds,
    _t: Date.now(),
  });
}

/**
 * 获取挂起状态的抄表记录列表
 * @param params 查询参数
 * @returns 抄表记录列表
 */
export function getPendingRecordList(params?: Record<string, any>) {
  return requestClient.get(Api.pendingList, {
    params,
  });
}
