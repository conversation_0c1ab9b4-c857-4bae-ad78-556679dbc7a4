import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const leaveTypeOptions = [
  { label: '病假', value: '1' },
  { label: '事假', value: '2' },
  { label: '年假', value: '3' },
  { label: '婚假', value: '4' },
  { label: '产假', value: '5' },
  { label: '其他', value: '7' },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'searchValue',
    label: '关键字',
    componentProps: {
      placeholder: '请输入户号或名称',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'useWaterNature',
    label: '用水性质',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_user_customer_nature'),
    },
    fieldName: 'customerNature',
    label: '客户性质',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('audit_status'),
    },
    fieldName: 'auditStatus',
    label: '审核状态',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: '用户ID',
  //   field: 'userId',
  // },
  {
    title: '户号',
    field: 'userNo',
  },
  {
    title: '用户名称',
    field: 'userName',
  },
  {
    title: '用水性质',
    field: 'useWaterNature',
    slots: {
      default: ({ row }) => {
        return renderDict(row.useWaterNature, 'waterfee_user_use_water_nature');
      },
    },
  },
  {
    title: '客户性质',
    field: 'customerNature',
    slots: {
      default: ({ row }) => {
        return renderDict(row.customerNature, 'waterfee_user_customer_nature');
      },
    },
  },
  {
    title: '抄表手册',
    field: 'bookName',
  },
  {
    title: '水表编号',
    field: 'meterNo',
  },
  {
    title: '用户状态',
    field: 'userStatus',
    slots: {
      default: ({ row }) => {
        return renderDict(row.userStatus, 'waterfee_user_user_status');
      },
    },
  },
  {
    title: '审核状态',
    field: 'auditStatus',
    slots: {
      default: ({ row }) => {
        return renderDict(row.auditStatus, 'wf_business_status');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 240,
  },
];

/**
 * 用户表单数据结构
 * 根据图片布局调整表单项的排列和样式
 * 只保留图片中显示的字段
 */
export const drawerSchema: FormSchemaGetter = () => [];

export const flowSchema: FormSchemaGetter = () => [
  // 隐藏字段
  // {
  //   component: 'Input',
  //   dependencies: {
  //     show: () => false,
  //     triggerFields: [''],
  //   },
  //   fieldName: 'userId',
  //   label: '主键',
  // },
  // 第一行
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '户号',
    componentProps: {
      placeholder: '输入内容',
      disabled: true,
    },
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_customer_nature'),
    },
    fieldName: 'customerNature',
    label: '客户性质',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'useWaterNature',
    label: '用水性质',
    rules: 'required',
  },
  // 第二行
  {
    component: 'Input',
    fieldName: 'useWaterNumber',
    label: '用水人数',
    componentProps: {
      placeholder: '输入内容',
    },
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'userName',
    label: '用户姓名',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'phoneNumber',
    label: '手机号码',
    componentProps: {
      placeholder: '输入内容',
    },
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'certificateNumber',
    label: '证件号码',
    componentProps: {
      placeholder: '输入内容',
    },
    rules: 'required',
  },
  // 第三行
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_certificate_type'),
    },
    fieldName: 'certificateType',
    label: '证件类型',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_user_status'),
    },
    fieldName: 'userStatus',
    label: '用户状态',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'areaId',
    label: '营业区域',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'communityId',
    label: '小区名称',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'unitRoomNumber',
    label: '单元房号',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'address',
    label: '用水地址',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'email',
    label: '电子邮箱',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'taxpayerIdentificationNumber',
    label: '纳税人识别号',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      format: 'YYYY-MM-DD HH:mm:ss',
      showTime: false,
    },
    fieldName: 'supplyDate',
    label: '供水日期',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'invoiceName',
    label: '开票名称',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_invoice_type'),
    },
    fieldName: 'invoiceType',
    label: '发票类型',
    rules: 'required',
  },
  // {
  //   component: 'Input',
  //   dependencies: {
  //     show: () => false,
  //     triggerFields: [''],
  //   },
  //   fieldName: 'meterNo',
  //   label: '水表编号',
  // },
  // {
  //   component: 'Select',
  //   dependencies: {
  //     show: () => false,
  //     triggerFields: [''],
  //   },
  //   fieldName: 'priceUseWaterNature',
  //   label: '价格-用水性质',
  // },
  // {
  //   component: 'Select',
  //   dependencies: {
  //     show: () => false,
  //     triggerFields: [''],
  //   },
  //   fieldName: 'billingMethod',
  //   label: '计费方式',
  // },
  // {
  //   component: 'RadioGroup',
  //   dependencies: {
  //     show: () => false,
  //     triggerFields: [''],
  //   },
  //   fieldName: 'ifPenalty',
  //   label: '是否有违约金',
  // },
  // {
  //   component: 'Select',
  //   dependencies: {
  //     show: () => false,
  //     triggerFields: [''],
  //   },
  //   fieldName: 'penaltyType',
  //   label: '违约金类型',
  // },
  // {
  //   component: 'RadioGroup',
  //   dependencies: {
  //     show: () => false,
  //     triggerFields: [''],
  //   },
  //   fieldName: 'ifExtraCharge',
  //   label: '是否有附加费',
  // },
  // {
  //   component: 'Select',
  //   dependencies: {
  //     show: () => false,
  //     triggerFields: [''],
  //   },
  //   fieldName: 'extraChargeType',
  //   label: '附加费内容',
  // },
];
